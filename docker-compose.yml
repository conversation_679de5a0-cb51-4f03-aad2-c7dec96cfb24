version: '3.8'

services:
  # Kong API Gateway (replaces nginx + api-gateway)
  kong:
    image: kong:3.6
    container_name: rag-kong-gateway
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_LOG_LEVEL: info
      KONG_PROXY_LISTEN: 0.0.0.0:8000
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_PLUGINS: bundled,prometheus
    volumes:
      - ./kong/kong.yml:/kong/declarative/kong.yml:ro
      - ./logs:/tmp:rw
    ports:
      - "80:8000"      # Main proxy port (replaces nginx:80)
      - "8000:8000"    # Also expose on 8000 for backward compatibility
      - "8001:8001"    # Admin API (for debugging, don't expose in production)
    depends_on:
      - chat-service
      - document-service
      - embedding-service
      - llm-service
      - frontend
      - redis
    networks:
      - rag-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Chat Service
  chat-service:
    build: 
      context: ./services/chat-service
      dockerfile: Dockerfile
    container_name: rag-chat-service
    ports:
      - "8001:8001"
    environment:
      - EMBEDDING_SERVICE_URL=http://embedding-service:8003
      - LLM_SERVICE_URL=http://llm-service:8004
      - QDRANT_URL=http://qdrant:6333
      - REDIS_URL=redis://redis:6379
      - MAX_CONTEXT_LENGTH=4096
      - RETRIEVAL_K=5
    volumes:
      - ./services/chat-service:/app
      - ./logs:/app/logs
    depends_on:
      - qdrant
      - redis
    networks:
      - rag-network
    restart: unless-stopped

  # Document Service
  document-service:
    build: 
      context: ./services/document-service
      dockerfile: Dockerfile
    container_name: rag-document-service
    ports:
      - "8002:8002"
    environment:
      - EMBEDDING_SERVICE_URL=http://embedding-service:8003
      - QDRANT_URL=http://qdrant:6333
      - REDIS_URL=redis://redis:6379
      - MAX_FILE_SIZE=50MB
      - SUPPORTED_FORMATS=pdf,txt,md,docx
    volumes:
      - ./services/document-service:/app
      - ./logs:/app/logs
      - ./documents:/app/documents
    depends_on:
      - qdrant
      - redis
    networks:
      - rag-network
    restart: unless-stopped

  # Embedding Service
  embedding-service:
    build:
      context: ./services/embedding-service
      dockerfile: Dockerfile
    container_name: rag-embedding-service
    ports:
      - "8003:8003"
    environment:
      - MODEL_PROVIDER=sentence_transformers
      - MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
      - DEVICE=cpu
      - BATCH_SIZE=32
      - REDIS_URL=redis://redis:6379
      - CACHE_TTL=3600
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOCAL_MODEL_URL=${LOCAL_MODEL_URL}
      - LOCAL_MODEL_API_KEY=${LOCAL_MODEL_API_KEY}
    volumes:
      - ./services/embedding-service:/app
      - ./models:/app/models
    depends_on:
      - redis
    networks:
      - rag-network
    restart: unless-stopped

  # LLM Service
  llm-service:
    build: 
      context: ./services/llm-service
      dockerfile: Dockerfile
    container_name: rag-llm-service
    ports:
      - "8004:8004"
    environment:
      - MODEL_NAME=microsoft/DialoGPT-medium
      - DEVICE=cpu
      - MAX_TOKENS=512
    volumes:
      - ./services/llm-service:/app
      - ./models:/app/models
    networks:
      - rag-network
    restart: unless-stopped

  # Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: rag-qdrant
    ports:
      - "6333:6333"
    volumes:
      - qdrant_storage:/qdrant/storage
    networks:
      - rag-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: rag-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - rag-network
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: rag-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - rag-network
    restart: unless-stopped

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: rag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - rag-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: rag-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - rag-network
    restart: unless-stopped

networks:
  rag-network:
    driver: bridge

volumes:
  qdrant_storage:
  redis_data:
  prometheus_data:
  grafana_data:

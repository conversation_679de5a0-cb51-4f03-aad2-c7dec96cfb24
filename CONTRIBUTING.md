# Contributing to RAG Chatbot System

We love your input! We want to make contributing to the RAG Chatbot System as easy and transparent as possible, whether it's:

- Reporting a bug
- Discussing the current state of the code
- Submitting a fix
- Proposing new features
- Becoming a maintainer

## Development Process

We use GitHub to host code, to track issues and feature requests, as well as accept pull requests.

## Pull Requests

1. Fork the repo and create your branch from `main`.
2. If you've added code that should be tested, add tests.
3. If you've changed APIs, update the documentation.
4. Ensure the test suite passes.
5. Make sure your code lints.
6. Issue that pull request!

## Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/rag-chatbot-system.git
   cd rag-chatbot-system
   ```

2. **Set up the development environment**
   ```bash
   ./scripts/start.sh
   ```

3. **Run tests**
   ```bash
   # Run health checks
   ./scripts/health-check.sh
   
   # Check individual services
   ./scripts/logs.sh [service-name]
   ```

## Code Style

* Use consistent code formatting
* Follow PEP 8 for Python code
* Use meaningful variable and function names
* Add comments for complex logic
* Keep functions small and focused

## Testing

* Write unit tests for new features
* Test API endpoints thoroughly
* Verify Docker builds work correctly
* Test with different document types

## Reporting Bugs

We use GitHub issues to track public bugs. Report a bug by opening a new issue.

**Great Bug Reports** tend to have:

- A quick summary and/or background
- Steps to reproduce
  - Be specific!
  - Give sample code if you can
- What you expected would happen
- What actually happens
- Notes (possibly including why you think this might be happening, or stuff you tried that didn't work)

## Feature Requests

We welcome feature requests! Please:

1. Check if the feature has already been requested
2. Provide a clear description of the problem you're trying to solve
3. Describe the solution you'd like
4. Consider alternative solutions
5. Provide any additional context

## Code of Conduct

### Our Pledge

We are committed to making participation in our project a harassment-free experience for everyone.

### Our Standards

Examples of behavior that contributes to creating a positive environment include:

* Using welcoming and inclusive language
* Being respectful of differing viewpoints and experiences
* Gracefully accepting constructive criticism
* Focusing on what is best for the community
* Showing empathy towards other community members

### Enforcement

Project maintainers are responsible for clarifying the standards of acceptable behavior and are expected to take appropriate and fair corrective action in response to any instances of unacceptable behavior.

## License

By contributing, you agree that your contributions will be licensed under the MIT License.

## Questions?

Don't hesitate to reach out! You can:

- Open an issue for questions about the code
- Start a discussion for broader topics
- Check our documentation in the README

## Development Guidelines

### Service Development

When developing new services:

1. Follow the existing service structure
2. Add proper health checks
3. Include comprehensive error handling
4. Add logging for debugging
5. Update docker-compose.yml
6. Document API endpoints

### Frontend Development

For frontend changes:

1. Use TypeScript for type safety
2. Follow React best practices
3. Add proper error boundaries
4. Test responsive design
5. Ensure accessibility compliance

### Infrastructure Changes

For infrastructure modifications:

1. Test with Docker Compose
2. Verify scaling capabilities
3. Update monitoring configurations
4. Document deployment changes
5. Consider security implications

Thank you for contributing! 🚀

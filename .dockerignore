# =================================================================
# RAG Chatbot System - .dockerignore
# =================================================================

# =================================================================
# Version Control
# =================================================================
.git
.gitignore
.gitattributes
.gitmodules

# =================================================================
# Documentation
# =================================================================
README.md
docs/
*.md
CHANGELOG.md
CONTRIBUTING.md
LICENSE

# =================================================================
# CI/CD
# =================================================================
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile
azure-pipelines.yml

# =================================================================
# IDE and Editor Files
# =================================================================
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
*.swp
*.swo
*~

# =================================================================
# OS Generated Files
# =================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# =================================================================
# Python
# =================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
htmlcov/

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# =================================================================
# Node.js / React
# =================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Build outputs
build/
dist/
.next/
out/

# Dependency directories
jspm_packages/

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Cache directories
.cache
.parcel-cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# =================================================================
# Docker
# =================================================================
Dockerfile*
docker-compose*.yml
.dockerignore

# =================================================================
# Application Data (Development)
# =================================================================
# Logs
logs/
*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Vector database storage
qdrant_storage/
vector_data/

# Redis data
redis_data/

# Monitoring data
prometheus_data/
grafana_data/

# =================================================================
# Models and Cache
# =================================================================
# Model files (these should be downloaded at runtime)
models/
model_cache/
.cache/
*.bin
*.safetensors

# Embeddings cache
embeddings_cache/

# Hugging Face cache
.cache/huggingface/

# =================================================================
# Uploads and Documents
# =================================================================
# Uploaded documents (except samples)
documents/uploaded/
uploads/
temp_uploads/

# Keep sample documents but ignore user uploads
documents/*.pdf
documents/*.docx
!documents/company-info.txt
!documents/faq.md

# =================================================================
# Temporary and Build Files
# =================================================================
tmp/
temp/
.tmp/
backup/
*.backup
*.bak

# =================================================================
# Secrets and Sensitive Data
# =================================================================
# Environment files with secrets
*.env.local
*.env.production
*.env.staging
api_keys.txt
secrets.json
config/secrets.yml
config/production.yml
secrets/

# SSL certificates
ssl/
*.crt
*.key
*.pem

# =================================================================
# Testing
# =================================================================
test-results/
coverage/
test-artifacts/
screenshots/
videos/

# =================================================================
# AI/ML Specific
# =================================================================
# Jupyter notebooks
.ipynb_checkpoints/

# Weights and Biases
wandb/

# MLflow
mlruns/
mlartifacts/

# TensorBoard
runs/
tensorboard_logs/

# Model checkpoints
checkpoints/
*.ckpt
*.pt
*.pth

# =================================================================
# Large Files
# =================================================================
*.mp4
*.avi
*.mov
*.mkv
*.large

# Compressed files
*.tar.gz
*.zip
*.7z
*.rar
*.tar.xz
*.bz2
*.gz

# Package files
*.deb
*.rpm
*.pkg
*.dmg

# =================================================================
# Development Tools
# =================================================================
# Scripts that are not needed in container
scripts/dev/
scripts/local/
tools/
utilities/

# =================================================================
# Kubernetes and Deployment
# =================================================================
k8s/
kubernetes/
helm/
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# =================================================================
# Monitoring and Observability (Config Only)
# =================================================================
# Keep config files but ignore data
monitoring/*/data/
monitoring/grafana/data/
monitoring/prometheus/data/

# =================================================================
# Project Specific Exclusions
# =================================================================
# Development scripts
scripts/setup.sh
scripts/dev-*.sh

# Sample data that's not needed in production
sample_data/
test_data/

# Local configuration overrides
docker-compose.override.yml
docker-compose.local.yml

# Development nginx configs
nginx/dev/

# =================================================================
# Keep Essential Files
# =================================================================
# Explicitly keep these files (override any exclusions above)
!requirements.txt
!package.json
!package-lock.json
!yarn.lock
!Dockerfile
!docker-compose.yml
!nginx/nginx.conf
!monitoring/prometheus/prometheus.yml

# =================================================================
# RAG Chatbot System - .gitignore
# =================================================================

# =================================================================
# Python
# =================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =================================================================
# Node.js / React
# =================================================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# =================================================================
# Docker
# =================================================================
# Docker volumes and data
volumes/
data/

# =================================================================
# IDEs and Editors
# =================================================================
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =================================================================
# OS Generated Files
# =================================================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~

# =================================================================
# Project Specific
# =================================================================
# Application logs
logs/
*.log

# Model files and caches
models/
model_cache/
.cache/
*.bin
*.safetensors

# Document uploads (for development)
documents/uploaded/
uploads/
temp_uploads/

# Database files
*.db
*.sqlite
*.sqlite3

# Vector database storage
qdrant_storage/
vector_data/
embeddings_cache/

# Redis data
redis_data/

# Monitoring data
prometheus_data/
grafana_data/

# SSL certificates (if any)
ssl/
*.crt
*.key
*.pem

# Backup files
backup/
*.backup
*.bak

# Temporary files
tmp/
temp/
.tmp/

# Configuration files with secrets
config/secrets.yml
config/production.yml
secrets/

# =================================================================
# AI/ML Specific
# =================================================================
# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Weights and Biases
wandb/

# MLflow
mlruns/
mlartifacts/

# TensorBoard logs
runs/
tensorboard_logs/

# Model checkpoints
checkpoints/
*.ckpt
*.pt
*.pth

# Hugging Face cache
.cache/huggingface/

# =================================================================
# Build and Deployment
# =================================================================
# Build artifacts
build/
dist/
*.tar.gz
*.zip

# Deployment files
docker-compose.override.yml
docker-compose.prod.yml.backup

# Kubernetes
k8s/secrets/
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# =================================================================
# Testing
# =================================================================
# Test results
test-results/
coverage/
.coverage
htmlcov/

# E2E test artifacts
test-artifacts/
screenshots/
videos/

# =================================================================
# Documentation
# =================================================================
# Generated documentation
docs/_build/
docs/build/
site/

# =================================================================
# Miscellaneous
# =================================================================
# Sensitive data
*.env.local
*.env.production
*.env.staging
api_keys.txt
secrets.json

# Large files that shouldn't be in git
*.mp4
*.avi
*.mov
*.mkv
*.large

# Package files
*.deb
*.rpm
*.pkg
*.dmg

# Compressed files
*.7z
*.rar
*.tar.xz
*.bz2
*.gz
*.zip

# =================================================================
# Project specific ignores
# =================================================================
# Ignore actual uploaded documents in production
documents/*.pdf
documents/*.txt
documents/*.md
documents/*.docx
!documents/company-info.txt
!documents/faq.md

# Ignore model downloads
models/*
!models/.gitkeep

# Ignore logs directory content but keep directory
logs/*
!logs/.gitkeep

# Ignore shared data
shared_data/*
!shared_data/.gitkeep

_format_version: "3.0"
_transform: true

# Services - Define upstream services
services:
  # Chat Service
  - name: chat-service
    url: http://chat-service:8001
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - rag-system
      - chat

  # Document Service  
  - name: document-service
    url: http://document-service:8002
    connect_timeout: 120000
    write_timeout: 120000
    read_timeout: 120000
    retries: 3
    tags:
      - rag-system
      - documents

  # Embedding Service
  - name: embedding-service
    url: http://embedding-service:8003
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - rag-system
      - embeddings

  # LLM Service
  - name: llm-service
    url: http://llm-service:8004
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - rag-system
      - llm

  # Frontend Service (for serving static files)
  - name: frontend-service
    url: http://frontend:3000
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - rag-system
      - frontend

# Routes - Define API routes
routes:
  # Frontend routes (serve React app)
  - name: frontend-root
    service: frontend-service
    paths: ["/"]
    methods: ["GET"]
    strip_path: false
    preserve_host: true
    tags:
      - frontend

  - name: frontend-static
    service: frontend-service
    paths: ["/static"]
    methods: ["GET"]
    strip_path: false
    preserve_host: true
    tags:
      - frontend

  - name: frontend-assets
    service: frontend-service
    paths: ["/assets", "/favicon.ico", "/manifest.json"]
    methods: ["GET"]
    strip_path: false
    preserve_host: true
    tags:
      - frontend

  # Chat Service routes
  - name: chat-endpoint
    service: chat-service
    paths: ["/api/chat"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - chat

  - name: chat-health
    service: chat-service
    paths: ["/api/chat/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # Document Service routes
  - name: documents-list
    service: document-service
    paths: ["/api/documents"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - documents

  - name: documents-upload
    service: document-service
    paths: ["/api/documents/upload"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - documents

  - name: documents-delete
    service: document-service
    paths: ["/api/documents/(?<doc_id>[^/]+)"]
    methods: ["DELETE", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - documents

  - name: documents-health
    service: document-service
    paths: ["/api/documents/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # Embedding Service routes
  - name: embeddings-generate
    service: embedding-service
    paths: ["/api/embed"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - embeddings

  - name: embeddings-models
    service: embedding-service
    paths: ["/api/embed/models"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - embeddings

  - name: embeddings-switch-model
    service: embedding-service
    paths: ["/api/embed/switch-model"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - embeddings

  - name: embeddings-health
    service: embedding-service
    paths: ["/api/embed/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # LLM Service routes
  - name: llm-generate
    service: llm-service
    paths: ["/api/llm/generate"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - llm

  - name: llm-models
    service: llm-service
    paths: ["/api/llm/models"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - llm

  - name: llm-health
    service: llm-service
    paths: ["/api/llm/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # Global health endpoint
  - name: global-health
    service: chat-service
    paths: ["/api/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

# Global plugins
plugins:
  # CORS for all routes
  - name: cors
    config:
      origins: ["*"]
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
      headers: ["Accept", "Accept-Version", "Content-Length", "Content-MD5", "Content-Type", "Date", "X-Auth-Token", "Authorization"]
      exposed_headers: ["X-Auth-Token"]
      credentials: true
      max_age: 3600
      preflight_continue: false
    tags:
      - global

  # Rate limiting
  - name: rate-limiting
    config:
      minute: 1000
      hour: 10000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
    tags:
      - global

  # Request size limiting (important for document uploads)
  - name: request-size-limiting
    config:
      allowed_payload_size: 100  # 100MB for document uploads
    tags:
      - global

  # Prometheus metrics
  - name: prometheus
    config:
      per_consumer: false
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
    tags:
      - global
      - monitoring

  # Logging
  - name: file-log
    config:
      path: "/tmp/access.log"
      reopen: true
    tags:
      - global
      - logging

# Consumer groups (for future authentication)
consumer_groups:
  - name: api-users
    tags:
      - users

# Consumers (for future API key authentication)
consumers:
  - username: system-admin
    custom_id: admin-001
    tags:
      - admin
    groups:
      - name: api-users

_format_version: "3.0"
_transform: true

# Services - Define upstream services
services:
  # Chat Service
  - name: chat-service
    url: http://chat-service:8001
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - rag-system
      - chat

  # Document Service  
  - name: document-service
    url: http://document-service:8002
    connect_timeout: 120000
    write_timeout: 120000
    read_timeout: 120000
    retries: 3
    tags:
      - rag-system
      - documents

  # Embedding Service
  - name: embedding-service
    url: http://embedding-service:8003
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - rag-system
      - embeddings

  # LLM Service
  - name: llm-service
    url: http://llm-service:8004
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - rag-system
      - llm

  # Authentication Service
  - name: auth-service
    url: http://auth-service:8005
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - rag-system
      - auth

  # Frontend Service (for serving static files)
  - name: frontend-service
    url: http://frontend:3000
    connect_timeout: 30000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - rag-system
      - frontend

# Routes - Define API routes
routes:
  # Frontend routes (serve React app)
  - name: frontend-root
    service: frontend-service
    paths: ["/"]
    methods: ["GET"]
    strip_path: false
    preserve_host: true
    tags:
      - frontend

  - name: frontend-static
    service: frontend-service
    paths: ["/static"]
    methods: ["GET"]
    strip_path: false
    preserve_host: true
    tags:
      - frontend

  - name: frontend-assets
    service: frontend-service
    paths: ["/assets", "/favicon.ico", "/manifest.json"]
    methods: ["GET"]
    strip_path: false
    preserve_host: true
    tags:
      - frontend

  # Authentication Service routes (public - no JWT required)
  - name: auth-register
    service: auth-service
    paths: ["/api/auth/register"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - auth
      - public

  - name: auth-login
    service: auth-service
    paths: ["/api/auth/login"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - auth
      - public

  - name: auth-google-url
    service: auth-service
    paths: ["/api/auth/google/auth-url"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - auth
      - public

  - name: auth-google-callback
    service: auth-service
    paths: ["/api/auth/google/callback"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - auth
      - public

  - name: auth-health
    service: auth-service
    paths: ["/api/auth/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health
      - public

  # Authentication Service routes (protected - JWT required)
  - name: auth-me
    service: auth-service
    paths: ["/api/auth/me"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - auth
      - protected

  - name: auth-logout
    service: auth-service
    paths: ["/api/auth/logout"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - auth
      - protected

  # Chat Service routes (protected - JWT required)
  - name: chat-endpoint
    service: chat-service
    paths: ["/api/chat"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - chat
      - protected

  - name: chat-health
    service: chat-service
    paths: ["/api/chat/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # Document Service routes (protected - JWT required)
  - name: documents-list
    service: document-service
    paths: ["/api/documents"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - documents
      - protected

  - name: documents-upload
    service: document-service
    paths: ["/api/documents/upload"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - documents
      - protected

  - name: documents-delete
    service: document-service
    paths: ["/api/documents/(?<doc_id>[^/]+)"]
    methods: ["DELETE", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - documents
      - protected

  - name: documents-health
    service: document-service
    paths: ["/api/documents/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # Embedding Service routes (protected - JWT required)
  - name: embeddings-generate
    service: embedding-service
    paths: ["/api/embed"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - embeddings
      - protected

  - name: embeddings-models
    service: embedding-service
    paths: ["/api/embed/models"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - embeddings
      - protected

  - name: embeddings-switch-model
    service: embedding-service
    paths: ["/api/embed/switch-model"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - embeddings
      - protected

  - name: embeddings-health
    service: embedding-service
    paths: ["/api/embed/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # LLM Service routes (protected - JWT required)
  - name: llm-generate
    service: llm-service
    paths: ["/api/llm/generate"]
    methods: ["POST", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - llm
      - protected

  - name: llm-models
    service: llm-service
    paths: ["/api/llm/models"]
    methods: ["GET", "OPTIONS"]
    strip_path: true
    tags:
      - api
      - llm
      - protected

  - name: llm-health
    service: llm-service
    paths: ["/api/llm/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

  # Global health endpoint
  - name: global-health
    service: chat-service
    paths: ["/api/health"]
    methods: ["GET"]
    strip_path: true
    tags:
      - api
      - health

# Global plugins
plugins:
  # CORS for all routes
  - name: cors
    config:
      origins: ["*"]
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
      headers: ["Accept", "Accept-Version", "Content-Length", "Content-MD5", "Content-Type", "Date", "X-Auth-Token", "Authorization"]
      exposed_headers: ["X-Auth-Token"]
      credentials: true
      max_age: 3600
      preflight_continue: false
    tags:
      - global

  # Rate limiting
  - name: rate-limiting
    config:
      minute: 1000
      hour: 10000
      policy: local
      fault_tolerant: true
      hide_client_headers: false
    tags:
      - global

  # Request size limiting (important for document uploads)
  - name: request-size-limiting
    config:
      allowed_payload_size: 100  # 100MB for document uploads
    tags:
      - global

  # Prometheus metrics
  - name: prometheus
    config:
      per_consumer: false
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
    tags:
      - global
      - monitoring

  # Logging
  - name: file-log
    config:
      path: "/tmp/access.log"
      reopen: true
    tags:
      - global
      - logging

  # JWT Authentication for protected routes
  - name: jwt
    route: chat-endpoint
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: auth-me
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: auth-logout
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: documents-list
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: documents-upload
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: documents-delete
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: embeddings-generate
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: embeddings-models
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: embeddings-switch-model
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: llm-generate
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

  - name: jwt
    route: llm-models
    config:
      secret_is_base64: false
      key_claim_name: iss
      algorithm: HS256
    tags:
      - auth
      - jwt

# JWT Secrets for authentication
jwt_secrets:
  - consumer: null  # Global JWT secret
    key: "rag-chatbot-jwt-issuer"
    secret: "your-super-secure-jwt-secret-key-here"  # Should match JWT_SECRET_KEY in auth service
    algorithm: "HS256"
    tags:
      - jwt
      - auth

# Consumer groups (for future role-based access)
consumer_groups:
  - name: api-users
    tags:
      - users
  - name: admin-users
    tags:
      - admin

# Consumers (JWT-based authentication)
consumers:
  - username: jwt-auth
    custom_id: jwt-001
    tags:
      - jwt
      - auth

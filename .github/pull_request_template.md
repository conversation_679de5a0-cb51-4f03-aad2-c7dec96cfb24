## 🎯 Description
Brief description of what this PR does.

## 🔗 Related Issue
Fixes # (issue number)

## 🛠️ Type of Change
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📝 Documentation update
- [ ] 🔧 Configuration change
- [ ] 🧹 Code refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test addition or improvement

## 📋 Changes Made
- Change 1
- Change 2
- Change 3

## 🧪 Testing
- [ ] Tests pass locally with my changes
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] I have tested the Docker build and containers work correctly

## 📷 Screenshots (if applicable)
Add screenshots to show the changes in action.

## ✅ Checklist
- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published in downstream modules

## 🚀 Deployment
- [ ] This change requires a documentation update
- [ ] This change requires environment variable updates
- [ ] This change requires database migrations
- [ ] This change affects Docker configuration

## 📊 Performance Impact
- [ ] No performance impact
- [ ] Slight performance improvement
- [ ] Significant performance improvement
- [ ] Potential performance regression (explain below)

## 🔒 Security Impact
- [ ] No security implications
- [ ] Improves security
- [ ] Potential security concerns (explain below)

## 🎭 Breaking Changes
If this introduces breaking changes, please describe the impact and migration path:

## 📝 Additional Notes
Any additional information, concerns, or questions about this PR.

## 🙏 Review Requests
@mention specific people you'd like to review this PR

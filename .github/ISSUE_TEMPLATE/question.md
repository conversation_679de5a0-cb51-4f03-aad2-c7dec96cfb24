---
name: Question/Support
about: Ask a question or get support
title: '[QUESTION] '
labels: 'question'
assignees: ''
---

## ❓ Question
What would you like to know?

## 🎯 Context
Please provide context about what you're trying to achieve:
- What are you building?
- What's your use case?
- What have you tried so far?

## 🖥️ Environment
- OS: [e.g. macOS, Ubuntu, Windows]
- Docker Version: [e.g. 20.10.12]
- RAM: [e.g. 8GB, 16GB]

## 📋 Current Setup
Describe your current setup:
- Which services are you running?
- Any configuration changes you've made?
- Document types you're working with?

## 🔍 What You've Tried
Please list what you've already tried:
- [ ] Checked the README
- [ ] Ran health checks: `./scripts/health-check.sh`
- [ ] Checked service logs: `./scripts/logs.sh`
- [ ] Searched existing issues
- [ ] Checked the documentation

## 📊 Logs/Output
If applicable, include relevant logs or error messages:
```
Paste logs here
```

## 🎯 Expected Outcome
What result are you hoping to achieve?

## 🤝 Additional Context
Any other information that might be helpful.

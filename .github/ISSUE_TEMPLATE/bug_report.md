---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📷 Screenshots
If applicable, add screenshots to help explain your problem.

## 🖥️ Environment
- OS: [e.g. macOS 12.0, Ubuntu 20.04, Windows 10]
- Docker Version: [e.g. 20.10.12]
- Docker Compose Version: [e.g. 1.29.2]
- Browser: [e.g. Chrome 96, Firefox 95] (if frontend issue)

## 📋 Service Logs
If applicable, paste relevant logs from:
```bash
# Check service health
./scripts/health-check.sh

# Get specific service logs
./scripts/logs.sh [service-name]
```

## 📊 System Resources
- RAM: [e.g. 8GB, 16GB]
- CPU: [e.g. Intel i5, M1, AMD Ryzen]
- Available Storage: [e.g. 50GB]

## 🔧 Additional Context
Add any other context about the problem here.

## 🤔 Possible Solution
If you have ideas on how to fix this, please share them.

# 🤖 RAG Chatbot System

A production-ready Retrieval-Augmented Generation (RAG) chatbot system built with microservices architecture, designed to answer questions based on your uploaded documents.

## 🏗️ System Architecture

### High-Level Architecture Diagram

```
┌──────────────────────────────────────────────────────────────────┐
│                          Load Balancer                          │
│                         (Nginx Reverse Proxy)                  │
└─────────────────────┬────────────────────────────────────────────┘
                      │
┌─────────────────────┴────────────────────────────────────────────┐
│                      API Gateway                                │
│              (Authentication, Rate Limiting, Routing)          │
└─┬─────────┬─────────┬─────────────┬─────────────┬──────────────┘
  │         │         │             │             │
  ▼         ▼         ▼             ▼             ▼
┌───────┐ ┌───────┐ ┌─────────────┐ ┌───────────┐ ┌──────────────┐
│  Web  │ │  Chat │ │   Document  │ │ Embedding │ │   LLM/vLLM   │
│  UI   │ │Service│ │   Service   │ │  Service  │ │   Service    │
│(React)│ │(RAG)  │ │(Processing) │ │(Vectors)  │ │(Generation)  │
└───────┘ └───┬───┘ └─────┬───────┘ └─────┬─────┘ └──────┬───────┘
              │           │               │              │
              └───────────┼───────────────┼──────────────┘
                          │               │
                          ▼               ▼
                    ┌─────────────┐ ┌─────────────┐
                    │   Vector    │ │    Redis    │
                    │  Database   │ │   Cache     │
                    │  (Qdrant)   │ │             │
                    └─────────────┘ └─────────────┘

              ┌─────────────┐ ┌─────────────┐
              │ Prometheus  │ │   Grafana   │
              │ (Metrics)   │ │(Monitoring) │
              └─────────────┘ └─────────────┘
```

### Service Components

#### Core Services
- **API Gateway** (Port 8000): Central routing, authentication, rate limiting
- **Chat Service** (Port 8001): Core RAG logic and conversation management  
- **Document Service** (Port 8002): Document processing and ingestion
- **Embedding Service** (Port 8003): Text embedding generation
- **LLM Service** (Port 8004): Language model inference

#### Infrastructure
- **Vector Database** (Qdrant - Port 6333): Similarity search and document storage
- **Cache** (Redis - Port 6379): Performance optimization and session storage
- **Frontend** (React - Port 3000): User interface
- **Load Balancer** (Nginx - Port 80): Request distribution and SSL termination
- **Monitoring** (Prometheus/Grafana - Ports 9090/3001): System metrics and dashboards

### Data Flow Architecture

#### Document Upload Flow
```
1. User uploads document → Frontend
2. Frontend → API Gateway → Document Service
3. Document Service processes file (PDF/TXT/MD)
4. Text chunking and preprocessing
5. Document Service → Embedding Service (generate vectors)
6. Embedding Service → Vector Database (store embeddings)
7. Confirmation → User
```

#### Question-Answer Flow
```
1. User asks question → Frontend
2. Frontend → API Gateway → Chat Service
3. Chat Service → Embedding Service (query embedding)
4. Chat Service → Vector Database (similarity search)
5. Retrieve relevant document chunks
6. Chat Service → LLM Service (generate response)
7. Response with sources → User
```

### Microservices Design Patterns

#### Service Discovery
- Docker Compose networking for service communication
- Environment-based service URLs
- Health check endpoints for service monitoring

#### Data Management
- **Event-driven**: Asynchronous document processing
- **CQRS**: Separate read/write operations for chat and documents
- **Caching**: Redis for frequently accessed data

#### Scalability Patterns
- **Horizontal Scaling**: Independent service scaling
- **Load Balancing**: Nginx for traffic distribution
- **Caching Strategy**: Multi-level caching (Redis + in-memory)
- **Async Processing**: Background tasks for heavy operations

### Technology Stack

| Layer | Technology | Purpose |
|-------|------------|----------|
| **Frontend** | React + TypeScript | User interface |
| **API Layer** | FastAPI | REST API services |
| **Processing** | LangChain | Document processing |
| **ML/AI** | Transformers, Sentence-Transformers | NLP and embeddings |
| **Database** | Qdrant (Vector), Redis (Cache) | Data storage |
| **Infrastructure** | Docker, Docker Compose | Containerization |
| **Monitoring** | Prometheus, Grafana | Observability |
| **Proxy** | Nginx | Load balancing |

### Security Architecture

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│    Nginx    │───▶│ API Gateway │
│ (Browser)   │    │ (SSL Term.) │    │ (Auth/Rate) │
└─────────────┘    └─────────────┘    └─────────────┘
                                             │
                   ┌─────────────────────────┼─────────────────────────┐
                   │                         ▼                         │
            ┌──────────────┐    ┌──────────────┐    ┌──────────────┐    │
            │   Services   │    │   Services   │    │   Services   │    │
            │  (Internal)  │    │  (Internal)  │    │  (Internal)  │    │
            └──────────────┘    └──────────────┘    └──────────────┘    │
                   │                         │                         │
                   └─────────────────────────┼─────────────────────────┘
                                             ▼
                                    ┌─────────────┐
                                    │  Databases  │
                                    │ (Encrypted) │
                                    └─────────────┘
```

### Performance Considerations

#### Optimization Strategies
- **Caching**: Multi-level caching (Redis, in-memory)
- **Connection Pooling**: Database connection optimization
- **Async Processing**: Non-blocking I/O operations
- **Resource Management**: CPU/memory allocation per service
- **Model Optimization**: Quantization and optimization for inference

#### Scaling Strategies
- **Horizontal**: Scale individual services based on load
- **Vertical**: Increase resources for compute-intensive services
- **Geographic**: Deploy in multiple regions for global access
- **Auto-scaling**: Based on metrics like CPU, memory, request rate

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- 8GB+ RAM recommended
- 20GB+ available storage

### Installation

1. **Navigate to project directory**
   ```bash
   cd /Users/<USER>/AI-Path/Long-chatbot
   ```

2. **Start the system**
   ```bash
   ./scripts/start.sh
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - API Gateway: http://localhost:8000
   - Grafana: http://localhost:3001
   - Prometheus: http://localhost:9090

### First Steps

1. Upload documents (PDF, TXT, MD files)
2. Wait for processing to complete
3. Start asking questions about your documents!

## 📋 Management Commands

```bash
# Start the system
./scripts/start.sh

# Stop the system  
./scripts/stop.sh

# Check health status
./scripts/health-check.sh

# View logs (all services)
./scripts/logs.sh

# View logs (specific service)
./scripts/logs.sh chat-service
```

## 🔧 Configuration

Key environment variables in `.env`:

- `MODEL_NAME`: LLM model to use
- `EMBEDDING_MODEL`: Embedding model for documents
- `DEVICE`: cpu or cuda
- `MAX_FILE_SIZE`: Maximum upload size
- `RETRIEVAL_K`: Number of documents to retrieve

## 📊 Monitoring

- **Grafana Dashboard**: http://localhost:3001 (admin/admin123)
- **Prometheus Metrics**: http://localhost:9090
- **Health Checks**: All services expose `/health` endpoints

## 🐛 Troubleshooting

### Common Issues

**Services not starting:**
```bash
docker-compose logs -f
./scripts/health-check.sh
```

**Slow responses:**
- Check system resources
- Reduce document complexity
- Verify model configuration

**Upload failures:**
- Check file format (PDF, TXT, MD only)
- Verify file size < 50MB
- Check available storage

### Getting Help

1. Check service logs: `./scripts/logs.sh [service-name]`
2. Verify health status: `./scripts/health-check.sh`
3. Restart services: `./scripts/stop.sh && ./scripts/start.sh`

## 🔒 Security

- JWT-based authentication
- Input validation and sanitization
- Rate limiting on API endpoints
- Secure document storage
- No external API calls (runs completely local)

## 🚀 Production Deployment

For production use:

1. Update security settings in `.env`
2. Configure proper SSL certificates
3. Set up external monitoring
4. Implement backup strategies
5. Scale services as needed

## 📈 Performance Optimization

- **GPU Support**: Change `DEVICE=cuda` in `.env`
- **Scaling**: Use `docker-compose up --scale chat-service=3`
- **Caching**: Redis caches embeddings and responses
- **Load Balancing**: Nginx distributes traffic

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Quick Contribution Steps
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test them
4. Commit your changes: `git commit -m 'Add amazing feature'`
5. Push to the branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

### Development

To modify services:

1. Edit code in `services/[service-name]/`
2. Restart specific service: `docker-compose restart [service-name]`
3. View logs: `./scripts/logs.sh [service-name]`

### Issues and Support

- 🐛 **Bug Reports**: [Open an issue](https://github.com/your-username/rag-chatbot-system/issues)
- 💡 **Feature Requests**: [Start a discussion](https://github.com/your-username/rag-chatbot-system/discussions)
- 📖 **Documentation**: Check our comprehensive [README](README.md)
- 💬 **Community**: Join our discussions for help and collaboration

## 📝 API Documentation

- API Gateway: http://localhost:8000/docs
- Individual services: http://localhost:800[1-4]/docs

## 🎯 Features

✅ **Production-Ready Microservices**
- API Gateway with authentication & rate limiting
- Scalable service architecture
- Health monitoring & logging

✅ **Advanced RAG Pipeline**
- Document processing (PDF, TXT, MD)
- Vector embeddings with caching
- Context-aware response generation
- Source attribution

✅ **Modern Tech Stack**
- FastAPI for high-performance APIs
- React frontend with real-time chat
- Qdrant for vector similarity search
- Redis for caching & sessions

✅ **DevOps Ready**
- Docker containerization
- Prometheus monitoring
- Grafana dashboards
- Load balancing with Nginx

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### What this means:
- ✅ **Commercial use** - Use it in your commercial projects
- ✅ **Modification** - Modify the code as needed
- ✅ **Distribution** - Share and distribute freely
- ✅ **Private use** - Use it privately without disclosure
- ⚠️ **Liability** - No warranty provided
- ⚠️ **License notice** - Include original license in distributions

## 🚀 Roadmap

### Coming Soon
- [ ] Multi-language document support
- [ ] Advanced authentication (OAuth, SAML)
- [ ] Kubernetes deployment configurations
- [ ] Advanced analytics dashboard
- [ ] API rate limiting per user
- [ ] Document versioning
- [ ] Integration with cloud storage (S3, GCS)

### Future Enhancements
- [ ] Multi-modal support (images, audio)
- [ ] Advanced RAG techniques (graph RAG)
- [ ] Enterprise features (RBAC, audit logs)
- [ ] Performance optimizations
- [ ] Mobile application

See our [Issues](https://github.com/your-username/rag-chatbot-system/issues) for detailed feature requests and bug reports.

## 🙏 Acknowledgments

Built with:
- FastAPI for API services
- LangChain for document processing
- Qdrant for vector storage
- React for frontend
- Docker for containerization

---

**Happy coding! 🚀**

For questions or support, check the health status first:
```bash
./scripts/health-check.sh
```

# RAG Chatbot System Environment Configuration

# ===== GENERAL CONFIGURATION =====
# JWT secret for authentication (generate a secure random string)
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here

# ===== EMBEDDING SERVICE CONFIGURATION =====
# Choose provider: sentence_transformers, openai, local
MODEL_PROVIDER=sentence_transformers
MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2

# OpenAI Configuration (if using OpenAI provider)
OPENAI_API_KEY=your-openai-api-key-here

# Local Model Configuration (if using local provider)
LOCAL_MODEL_URL=http://localhost:11434
LOCAL_MODEL_API_KEY=optional-local-api-key

# ===== MONITORING CONFIGURATION =====
# Grafana admin password
GRAFANA_PASSWORD=admin

# ===== KONG GATEWAY CONFIGURATION =====
# Kong is configured via kong/kong.yml (declarative config)
# No additional environment variables needed for basic setup

# ===== EXAMPLE CONFIGURATIONS =====

# Example 1: OpenAI Embeddings
# MODEL_PROVIDER=openai
# MODEL_NAME=text-embedding-3-large
# OPENAI_API_KEY=sk-your-openai-key-here

# Example 2: Local Llama Model (via Ollama)
# MODEL_PROVIDER=local
# MODEL_NAME=llama3.2:3b
# LOCAL_MODEL_URL=http://localhost:11434

# Example 3: Local GPT-OSS Model
# MODEL_PROVIDER=local
# MODEL_NAME=gpt-oss:20b
# LOCAL_MODEL_URL=http://localhost:8080
# LOCAL_MODEL_API_KEY=your-local-api-key

# ===== DEVELOPMENT SETTINGS =====
# Set to 'development' for verbose logging
ENVIRONMENT=production

# ===== SECURITY NOTES =====
# 1. Generate a strong JWT_SECRET_KEY (at least 32 characters)
# 2. Keep API keys secure and never commit them to version control
# 3. In production, consider using Kong's key-auth or JWT plugins
# 4. Disable Kong Admin API (port 8001) in production

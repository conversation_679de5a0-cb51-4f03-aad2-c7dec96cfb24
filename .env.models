# Model Configuration for RAG Chatbot System
# This file defines available models and their configurations

# =================================================================
# LLM Models Configuration
# =================================================================

# OpenAI Models (requires API key)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic Claude Models (requires API key)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_BASE_URL=https://api.anthropic.com

# Local Models (vLLM, Ollama, or direct inference)
LOCAL_MODEL_TYPE=transformers  # Options: vllm, ollama, transformers
LOCAL_MODEL_NAME=microsoft/DialoGPT-medium
LOCAL_MODEL_PATH=/app/models/  # For locally downloaded models

# vLLM Configuration (for high-performance local inference)
VLLM_MODEL=meta-llama/Llama-2-7b-chat-hf
VLLM_TENSOR_PARALLEL_SIZE=1
VLLM_GPU_MEMORY_UTILIZATION=0.9
VLLM_MAX_MODEL_LEN=4096
VLLM_TRUST_REMOTE_CODE=false

# Ollama Configuration (for easy local model management)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2:3b
OLLAMA_KEEP_ALIVE=5m

# Hugging Face Transformers (direct inference)
HF_MODEL=microsoft/DialoGPT-medium
HF_CACHE_DIR=/app/models/huggingface
HF_USE_AUTH_TOKEN=false
HF_TOKEN=your-huggingface-token-here

# Model Provider Selection
# Options: openai, anthropic, local_vllm, local_ollama, local_transformers
LLM_PROVIDER=local_transformers

# =================================================================
# Embedding Models Configuration
# =================================================================

# Embedding Provider Selection
# Options: openai, local_sentence_transformers, local_instructor
EMBEDDING_PROVIDER=local_sentence_transformers

# OpenAI Embeddings
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=1536

# Local Sentence Transformers
SENTENCE_TRANSFORMER_MODEL=sentence-transformers/all-MiniLM-L6-v2
SENTENCE_TRANSFORMER_CACHE_DIR=/app/models/sentence_transformers

# Alternative embedding models (uncomment to use)
# SENTENCE_TRANSFORMER_MODEL=sentence-transformers/all-mpnet-base-v2
# SENTENCE_TRANSFORMER_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
# SENTENCE_TRANSFORMER_MODEL=BAAI/bge-small-en-v1.5
# SENTENCE_TRANSFORMER_MODEL=intfloat/e5-small-v2

# Instructor Embeddings (for specialized domains)
INSTRUCTOR_MODEL=hkunlp/instructor-base
INSTRUCTOR_INSTRUCTION="Represent the document for retrieval:"

# =================================================================
# Model Performance Configuration
# =================================================================

# Generation Parameters
MAX_TOKENS=512
TEMPERATURE=0.1
TOP_P=0.9
TOP_K=50
REPETITION_PENALTY=1.1
FREQUENCY_PENALTY=0.0
PRESENCE_PENALTY=0.0

# Batch Processing
EMBEDDING_BATCH_SIZE=32
LLM_BATCH_SIZE=1
MAX_CONCURRENT_REQUESTS=10

# Context Management
MAX_CONTEXT_LENGTH=4096
CONTEXT_WINDOW_RATIO=0.8  # Use 80% of context for retrieval, 20% for generation

# =================================================================
# Hardware Configuration
# =================================================================

# Device Selection
DEVICE=cpu  # Options: cpu, cuda, mps (for Apple Silicon)
DEVICE_MAP=auto  # For multi-GPU setups

# Memory Management
MAX_MEMORY_PER_GPU=0.8
CPU_OFFLOAD=false
LOAD_IN_8BIT=false
LOAD_IN_4BIT=false

# =================================================================
# Advanced Model Configuration
# =================================================================

# Model Quantization (for memory efficiency)
USE_QUANTIZATION=false
QUANTIZATION_TYPE=dynamic  # Options: dynamic, static, qint8, qint4

# Model Optimization
USE_TORCH_COMPILE=false
USE_BETTER_TRANSFORMER=false
USE_FLASH_ATTENTION=false

# Custom Model Endpoints
CUSTOM_LLM_ENDPOINT=http://localhost:8080/v1/completions
CUSTOM_EMBEDDING_ENDPOINT=http://localhost:8081/v1/embeddings

# =================================================================
# Model-Specific Configurations
# =================================================================

# Llama Models
LLAMA_SYSTEM_PROMPT="You are a helpful assistant that answers questions based on the provided context."
LLAMA_CHAT_TEMPLATE=true

# Claude Models  
CLAUDE_MAX_TOKENS=4000
CLAUDE_SYSTEM_PROMPT="You are a helpful AI assistant that provides accurate answers based on the given context."

# GPT Models
GPT_SYSTEM_PROMPT="You are a helpful assistant that answers questions based on the provided context. Be concise and accurate."
GPT_RESPONSE_FORMAT=text

# Chinese Models (Qwen, ChatGLM, etc.)
CHINESE_MODEL_SUPPORT=false
CHINESE_MODEL_NAME=Qwen/Qwen-7B-Chat

# =================================================================
# Fallback Configuration
# =================================================================

# Fallback models in case primary model fails
FALLBACK_LLM_PROVIDER=local_transformers
FALLBACK_LLM_MODEL=microsoft/DialoGPT-small
FALLBACK_EMBEDDING_PROVIDER=local_sentence_transformers
FALLBACK_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# =================================================================
# Development and Testing
# =================================================================

# Mock Models (for testing without actual model inference)
USE_MOCK_MODELS=false
MOCK_RESPONSE_DELAY=1.0

# Model Loading
LAZY_LOADING=true  # Load models only when needed
PRELOAD_MODELS=false  # Preload models at startup

# Debug Mode
MODEL_DEBUG=false
LOG_MODEL_PERFORMANCE=true

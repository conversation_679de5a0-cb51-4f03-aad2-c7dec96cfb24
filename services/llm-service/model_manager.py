# services/llm-service/model_manager.py
# Flexible Model Manager - Supports Multiple LLM Providers

import os
import asyncio
import logging
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod
from datetime import datetime

# External providers
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

# Local inference
import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    pipeline,
    TextGenerationPipeline
)

# Optional imports for advanced features
try:
    import vllm
    VLLM_AVAILABLE = True
except ImportError:
    VLLM_AVAILABLE = False

try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False

logger = logging.getLogger(__name__)

class BaseLLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_loaded = False
        self.model_name = config.get('model_name', 'unknown')
    
    @abstractmethod
    async def load_model(self):
        """Load the model"""
        pass
    
    @abstractmethod
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text from prompt"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Check if the model is healthy"""
        pass

class OpenAIProvider(BaseLLMProvider):
    """OpenAI GPT models provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not installed. Install with: pip install openai")
        
        self.client = None
        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url', 'https://api.openai.com/v1')
        self.model_name = config.get('model', 'gpt-4-turbo-preview')
    
    async def load_model(self):
        """Initialize OpenAI client"""
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        self.client = openai.AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        self.is_loaded = True
        logger.info(f"OpenAI client initialized with model: {self.model_name}")
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text using OpenAI API"""
        if not self.is_loaded:
            await self.load_model()
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get('max_tokens', 512),
                temperature=kwargs.get('temperature', 0.1),
                top_p=kwargs.get('top_p', 0.9),
                frequency_penalty=kwargs.get('frequency_penalty', 0.0),
                presence_penalty=kwargs.get('presence_penalty', 0.0),
                stop=kwargs.get('stop', None)
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check OpenAI API health"""
        try:
            if not self.is_loaded:
                return {"status": "not_loaded", "model": self.model_name}
            
            # Simple test request
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=1
            )
            return {
                "status": "healthy",
                "model": self.model_name,
                "provider": "openai"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "model": self.model_name,
                "provider": "openai",
                "error": str(e)
            }

class AnthropicProvider(BaseLLMProvider):
    """Anthropic Claude models provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic package not installed. Install with: pip install anthropic")
        
        self.client = None
        self.api_key = config.get('api_key')
        self.model_name = config.get('model', 'claude-3-sonnet-20240229')
    
    async def load_model(self):
        """Initialize Anthropic client"""
        if not self.api_key:
            raise ValueError("Anthropic API key is required")
        
        self.client = anthropic.AsyncAnthropic(api_key=self.api_key)
        self.is_loaded = True
        logger.info(f"Anthropic client initialized with model: {self.model_name}")
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text using Anthropic API"""
        if not self.is_loaded:
            await self.load_model()
        
        try:
            response = await self.client.messages.create(
                model=self.model_name,
                max_tokens=kwargs.get('max_tokens', 512),
                temperature=kwargs.get('temperature', 0.1),
                top_p=kwargs.get('top_p', 0.9),
                messages=[{"role": "user", "content": prompt}],
                stop_sequences=kwargs.get('stop', None)
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Anthropic generation error: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Anthropic API health"""
        try:
            if not self.is_loaded:
                return {"status": "not_loaded", "model": self.model_name}
            
            # Simple test request
            response = await self.client.messages.create(
                model=self.model_name,
                max_tokens=1,
                messages=[{"role": "user", "content": "Hi"}]
            )
            return {
                "status": "healthy",
                "model": self.model_name,
                "provider": "anthropic"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "model": self.model_name,
                "provider": "anthropic",
                "error": str(e)
            }

class TransformersProvider(BaseLLMProvider):
    """Hugging Face Transformers provider for local inference"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.model_name = config.get('model', 'microsoft/DialoGPT-medium')
        self.device = config.get('device', 'cpu')
        self.cache_dir = config.get('cache_dir', '/app/models/huggingface')
    
    async def load_model(self):
        """Load Hugging Face model"""
        if self.is_loaded:
            return
        
        try:
            logger.info(f"Loading Transformers model: {self.model_name}")
            
            # Load in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._load_model_sync)
            
            self.is_loaded = True
            logger.info(f"Transformers model loaded: {self.model_name}")
        except Exception as e:
            logger.error(f"Error loading Transformers model: {e}")
            raise
    
    def _load_model_sync(self):
        """Synchronous model loading"""
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_name,
            cache_dir=self.cache_dir,
            trust_remote_code=True
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Load model
        if self.device == "cuda" and torch.cuda.is_available():
            device_map = "auto"
            torch_dtype = torch.float16
        else:
            device_map = None
            torch_dtype = torch.float32
        
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            cache_dir=self.cache_dir,
            torch_dtype=torch_dtype,
            device_map=device_map,
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        if device_map is None:
            self.model = self.model.to(self.device)
        
        # Create pipeline
        self.pipeline = pipeline(
            "text-generation",
            model=self.model,
            tokenizer=self.tokenizer,
            device=0 if self.device == "cuda" and torch.cuda.is_available() else -1,
            torch_dtype=torch_dtype
        )
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text using Transformers"""
        if not self.is_loaded:
            await self.load_model()
        
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._generate_sync,
                prompt,
                kwargs
            )
            return result
        except Exception as e:
            logger.error(f"Transformers generation error: {e}")
            raise
    
    def _generate_sync(self, prompt: str, kwargs: Dict) -> str:
        """Synchronous text generation"""
        generation_kwargs = {
            "max_new_tokens": kwargs.get('max_tokens', 512),
            "temperature": kwargs.get('temperature', 0.1),
            "top_p": kwargs.get('top_p', 0.9),
            "top_k": kwargs.get('top_k', 50),
            "repetition_penalty": kwargs.get('repetition_penalty', 1.1),
            "do_sample": kwargs.get('temperature', 0.1) > 0,
            "pad_token_id": self.tokenizer.eos_token_id,
            "eos_token_id": self.tokenizer.eos_token_id,
            "return_full_text": False
        }
        
        outputs = self.pipeline(prompt, **generation_kwargs)
        
        if isinstance(outputs, list) and len(outputs) > 0:
            generated_text = outputs[0]["generated_text"]
        else:
            generated_text = outputs["generated_text"]
        
        return generated_text.strip()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Transformers model health"""
        return {
            "status": "healthy" if self.is_loaded else "not_loaded",
            "model": self.model_name,
            "provider": "transformers",
            "device": self.device
        }

class ModelManager:
    """Central model manager that handles multiple providers"""
    
    def __init__(self):
        self.providers = {}
        self.active_provider = None
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        return {
            'llm_provider': os.getenv('LLM_PROVIDER', 'local_transformers'),
            'openai': {
                'api_key': os.getenv('OPENAI_API_KEY'),
                'model': os.getenv('OPENAI_MODEL', 'gpt-4-turbo-preview'),
                'base_url': os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
            },
            'anthropic': {
                'api_key': os.getenv('ANTHROPIC_API_KEY'),
                'model': os.getenv('ANTHROPIC_MODEL', 'claude-3-sonnet-20240229')
            },
            'transformers': {
                'model': os.getenv('LOCAL_MODEL_NAME', 'microsoft/DialoGPT-medium'),
                'device': os.getenv('DEVICE', 'cpu'),
                'cache_dir': os.getenv('HF_CACHE_DIR', '/app/models/huggingface')
            }
        }
    
    async def initialize_provider(self, provider_name: Optional[str] = None):
        """Initialize the specified provider or the default one"""
        provider_name = provider_name or self.config['llm_provider']
        
        if provider_name in self.providers:
            self.active_provider = self.providers[provider_name]
            return self.active_provider
        
        # Create provider based on name
        if provider_name == 'openai':
            provider = OpenAIProvider(self.config['openai'])
        elif provider_name == 'anthropic':
            provider = AnthropicProvider(self.config['anthropic'])
        elif provider_name == 'local_transformers':
            provider = TransformersProvider(self.config['transformers'])
        else:
            raise ValueError(f"Unknown provider: {provider_name}")
        
        await provider.load_model()
        self.providers[provider_name] = provider
        self.active_provider = provider
        
        return provider
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """Generate text using the active provider"""
        if not self.active_provider:
            await self.initialize_provider()
        
        return await self.active_provider.generate_text(prompt, **kwargs)
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all providers"""
        health_status = {
            "active_provider": self.config['llm_provider'],
            "providers": {}
        }
        
        for name, provider in self.providers.items():
            health_status["providers"][name] = await provider.health_check()
        
        if self.active_provider:
            health_status["active_status"] = await self.active_provider.health_check()
        
        return health_status
    
    async def switch_provider(self, provider_name: str):
        """Switch to a different provider"""
        if provider_name not in self.providers:
            await self.initialize_provider(provider_name)
        else:
            self.active_provider = self.providers[provider_name]
        
        logger.info(f"Switched to provider: {provider_name}")
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return ['openai', 'anthropic', 'local_transformers']
    
    def get_active_provider_info(self) -> Dict[str, Any]:
        """Get information about the active provider"""
        if not self.active_provider:
            return {"status": "no_active_provider"}
        
        return {
            "provider": self.config['llm_provider'],
            "model_name": self.active_provider.model_name,
            "is_loaded": self.active_provider.is_loaded
        }

# services/llm-service/main.py
# Flexible LLM Service - Supports Multiple Model Providers

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
import os
from model_manager import ModelManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="RAG LLM Service",
    description="Flexible language model service supporting multiple providers",
    version="2.0.0"
)

# Configuration
class Config:
    LLM_PROVIDER = os.getenv("LLM_PROVIDER", "local_transformers")
    MAX_TOKENS = int(os.getenv("MAX_TOKENS", "512"))
    TEMPERATURE = float(os.getenv("TEMPERATURE", "0.1"))

config = Config()

# Pydantic models
class GenerationRequest(BaseModel):
    prompt: str
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = 0.9
    top_k: Optional[int] = 50
    repetition_penalty: Optional[float] = 1.1
    stop: Optional[List[str]] = None
    provider: Optional[str] = None  # Allow runtime provider switching

class GenerationResponse(BaseModel):
    text: str
    model_name: str
    provider: str
    processing_time: float
    tokens_generated: Optional[int] = None

class ChatMessage(BaseModel):
    role: str  # "system", "user", "assistant"
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = 0.9
    stop: Optional[List[str]] = None
    provider: Optional[str] = None

class ChatResponse(BaseModel):
    message: ChatMessage
    model_name: str
    provider: str
    processing_time: float

class ModelInfo(BaseModel):
    active_provider: str
    model_name: str
    is_loaded: bool
    available_providers: List[str]
    provider_configs: Dict[str, Any]

class ProviderSwitchRequest(BaseModel):
    provider: str

# Global model manager
model_manager = ModelManager()

# Helper functions
def format_chat_prompt(messages: List[ChatMessage]) -> str:
    """Format chat messages into a prompt"""
    formatted_parts = []
    
    for message in messages:
        if message.role == "system":
            formatted_parts.append(f"System: {message.content}")
        elif message.role == "user":
            formatted_parts.append(f"User: {message.content}")
        elif message.role == "assistant":
            formatted_parts.append(f"Assistant: {message.content}")
    
    # Add assistant prefix for generation
    formatted_parts.append("Assistant:")
    
    return "\n".join(formatted_parts)

# API endpoints
@app.get("/health")
async def health_check():
    """Comprehensive health check for all providers"""
    try:
        health_status = await model_manager.health_check()
        
        # Add service-level information
        health_status.update({
            "service": "llm-service",
            "timestamp": datetime.utcnow().isoformat(),
            "config": {
                "default_provider": config.LLM_PROVIDER,
                "max_tokens": config.MAX_TOKENS,
                "temperature": config.TEMPERATURE
            }
        })
        
        # Determine overall status
        if health_status.get("active_status", {}).get("status") == "healthy":
            overall_status = "healthy"
        else:
            overall_status = "degraded"
        
        health_status["overall_status"] = overall_status
        
        return health_status
    
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "overall_status": "unhealthy",
            "service": "llm-service",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }

@app.post("/generate", response_model=GenerationResponse)
async def generate_text(request: GenerationRequest):
    """Generate text completion with flexible provider support"""
    try:
        start_time = datetime.utcnow()
        
        # Validate input
        if not request.prompt or not request.prompt.strip():
            raise HTTPException(status_code=400, detail="Empty prompt provided")
        
        if len(request.prompt) > 10000:
            raise HTTPException(status_code=400, detail="Prompt too long. Maximum: 10,000 characters")
        
        # Switch provider if requested
        if request.provider and request.provider != model_manager.config['llm_provider']:
            try:
                await model_manager.switch_provider(request.provider)
                logger.info(f"Switched to provider: {request.provider} for this request")
            except Exception as e:
                logger.error(f"Failed to switch provider to {request.provider}: {e}")
                raise HTTPException(status_code=400, detail=f"Failed to switch to provider {request.provider}: {str(e)}")
        
        # Generate text
        generated_text = await model_manager.generate_text(
            prompt=request.prompt,
            max_tokens=request.max_tokens or config.MAX_TOKENS,
            temperature=request.temperature or config.TEMPERATURE,
            top_p=request.top_p,
            top_k=request.top_k,
            repetition_penalty=request.repetition_penalty,
            stop=request.stop
        )
        
        # Calculate processing time
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        # Get provider info
        provider_info = model_manager.get_active_provider_info()
        
        # Estimate token count
        tokens_generated = len(generated_text.split()) if generated_text else 0
        
        return GenerationResponse(
            text=generated_text,
            model_name=provider_info.get("model_name", "unknown"),
            provider=provider_info.get("provider", "unknown"),
            processing_time=processing_time,
            tokens_generated=tokens_generated
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Text generation error: {e}")
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(e)}")

@app.post("/switch-provider")
async def switch_provider(request: ProviderSwitchRequest):
    """Switch to a different model provider"""
    try:
        available_providers = model_manager.get_available_providers()
        
        if request.provider not in available_providers:
            raise HTTPException(
                status_code=400, 
                detail=f"Unknown provider: {request.provider}. Available: {available_providers}"
            )
        
        await model_manager.switch_provider(request.provider)
        
        # Update the config
        model_manager.config['llm_provider'] = request.provider
        
        provider_info = model_manager.get_active_provider_info()
        
        return {
            "message": f"Successfully switched to provider: {request.provider}",
            "active_provider": provider_info.get("provider"),
            "model_name": provider_info.get("model_name"),
            "is_loaded": provider_info.get("is_loaded")
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Provider switch error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to switch provider: {str(e)}")

@app.get("/available-providers")
async def get_available_providers():
    """Get list of available model providers"""
    try:
        providers = model_manager.get_available_providers()
        
        # Check which providers are actually available
        provider_status = {}
        for provider in providers:
            try:
                if provider == 'openai':
                    provider_status[provider] = {
                        "available": bool(model_manager.config.get('openai', {}).get('api_key')),
                        "requires": "API key",
                        "models": ["gpt-4", "gpt-4-turbo-preview", "gpt-3.5-turbo"]
                    }
                elif provider == 'anthropic':
                    provider_status[provider] = {
                        "available": bool(model_manager.config.get('anthropic', {}).get('api_key')),
                        "requires": "API key",
                        "models": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]
                    }
                elif provider == 'local_transformers':
                    provider_status[provider] = {
                        "available": True,
                        "requires": "Local compute",
                        "models": ["microsoft/DialoGPT-medium", "meta-llama/Llama-2-7b-chat-hf", "Qwen/Qwen-7B-Chat"]
                    }
            except ImportError:
                provider_status[provider] = {
                    "available": False,
                    "requires": f"Install {provider} package",
                    "models": []
                }
        
        return {
            "available_providers": providers,
            "provider_status": provider_status,
            "current_provider": model_manager.config['llm_provider']
        }
    
    except Exception as e:
        logger.error(f"Available providers error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_stats():
    """Get service statistics and performance metrics"""
    try:
        provider_info = model_manager.get_active_provider_info()
        
        stats = {
            "timestamp": datetime.utcnow().isoformat(),
            "service_info": {
                "name": "llm-service",
                "version": "2.0.0"
            },
            "active_model": {
                "provider": provider_info.get("provider", "unknown"),
                "model_name": provider_info.get("model_name", "unknown"),
                "is_loaded": provider_info.get("is_loaded", False)
            },
            "configuration": {
                "max_tokens": config.MAX_TOKENS,
                "temperature": config.TEMPERATURE,
                "default_provider": config.LLM_PROVIDER
            }
        }
        
        # Add system information
        import torch
        stats["system_info"] = {
            "torch_version": torch.__version__,
            "cuda_available": torch.cuda.is_available(),
            "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
        }
        
        return stats
    
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    logger.info("Flexible LLM Service starting up...")
    logger.info(f"Default provider: {config.LLM_PROVIDER}")
    logger.info(f"Available providers: {model_manager.get_available_providers()}")
    
    # Initialize the default provider
    try:
        await model_manager.initialize_provider()
        provider_info = model_manager.get_active_provider_info()
        logger.info(f"Successfully initialized provider: {provider_info.get('provider')} with model: {provider_info.get('model_name')}")
    except Exception as e:
        logger.warning(f"Failed to initialize default provider on startup: {e}")
        logger.info("Provider will be initialized on first request")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("LLM Service shutting down...")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)

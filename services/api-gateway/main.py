# API Gateway Service - Central entry point for all requests
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
import httpx
import redis
import jwt
import os
import time
import logging
from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="RAG Chatbot API Gateway",
    description="Central API Gateway for RAG Chatbot System",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class Config:
    CHAT_SERVICE_URL = os.getenv("CHAT_SERVICE_URL", "http://localhost:8001")
    DOCUMENT_SERVICE_URL = os.getenv("DOCUMENT_SERVICE_URL", "http://localhost:8002")
    EMBEDDING_SERVICE_URL = os.getenv("EMBEDDING_SERVICE_URL", "http://localhost:8003")
    LLM_SERVICE_URL = os.getenv("LLM_SERVICE_URL", "http://localhost:8004")
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key")

config = Config()

try:
    redis_client = redis.from_url(config.REDIS_URL, decode_responses=True)
    redis_client.ping()
    logger.info("Connected to Redis")
except Exception as e:
    logger.error(f"Failed to connect to Redis: {e}")
    redis_client = None

http_client = httpx.AsyncClient(timeout=30.0)
security = HTTPBearer(auto_error=False)

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    user_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    sources: Optional[list] = None
    metadata: Optional[dict] = None

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "redis": "healthy" if redis_client else "unhealthy"
        }
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    try:
        response = await http_client.post(
            f"{config.CHAT_SERVICE_URL}/chat",
            json=request.dict(),
            timeout=60.0
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        
        return response.json()
    
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="Chat service timeout")
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/documents/upload")
async def upload_document(request: Request):
    try:
        form_data = await request.form()
        files = {}
        data = {}
        
        for key, value in form_data.items():
            if hasattr(value, 'read'):
                files[key] = (value.filename, await value.read(), value.content_type)
            else:
                data[key] = value
        
        response = await http_client.post(
            f"{config.DOCUMENT_SERVICE_URL}/upload",
            files=files,
            data=data,
            timeout=120.0
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=response.text)
        
        return response.json()
    
    except Exception as e:
        logger.error(f"Document upload error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/documents")
async def list_documents():
    try:
        response = await http_client.get(f"{config.DOCUMENT_SERVICE_URL}/documents")
        return response.json()
    except Exception as e:
        logger.error(f"List documents error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

#!/usr/bin/env python3
"""
Database initialization script for the authentication service
Creates tables and optionally seeds with test data
"""

import asyncio
import aiosqlite
import bcrypt
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

async def create_tables():
    """Create database tables"""
    db_path = Path("auth.db")
    
    async with aiosqlite.connect(db_path) as db:
        # Users table
        await db.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT,
                google_id TEXT UNIQUE,
                full_name TEXT,
                avatar_url TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # User sessions table (for token management)
        await db.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token_jti TEXT UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # User roles table (for future RBAC)
        await db.execute("""
            CREATE TABLE IF NOT EXISTS user_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Create indexes for better performance
        await db.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_sessions_token_jti ON user_sessions(token_jti)")
        
        await db.commit()
        logger.info("Database tables created successfully")

async def seed_test_data():
    """Seed database with test data"""
    db_path = Path("auth.db")
    
    async with aiosqlite.connect(db_path) as db:
        # Check if test user already exists
        async with db.execute("SELECT COUNT(*) FROM users WHERE username = ?", ("admin",)) as cursor:
            count = await cursor.fetchone()
            if count[0] > 0:
                logger.info("Test data already exists, skipping seed")
                return
        
        # Create test users
        test_users = [
            {
                "username": "admin",
                "email": "<EMAIL>",
                "password": "admin123",
                "full_name": "System Administrator",
                "role": "admin"
            },
            {
                "username": "testuser",
                "email": "<EMAIL>", 
                "password": "test123",
                "full_name": "Test User",
                "role": "user"
            },
            {
                "username": "demo",
                "email": "<EMAIL>",
                "password": "demo123", 
                "full_name": "Demo User",
                "role": "user"
            }
        ]
        
        for user_data in test_users:
            password_hash = hash_password(user_data["password"])
            
            # Insert user
            cursor = await db.execute("""
                INSERT INTO users (username, email, password_hash, full_name)
                VALUES (?, ?, ?, ?)
            """, (
                user_data["username"],
                user_data["email"],
                password_hash,
                user_data["full_name"]
            ))
            
            user_id = cursor.lastrowid
            
            # Insert user role
            await db.execute("""
                INSERT INTO user_roles (user_id, role)
                VALUES (?, ?)
            """, (user_id, user_data["role"]))
            
            logger.info(f"Created test user: {user_data['username']} ({user_data['email']})")
        
        await db.commit()
        logger.info("Test data seeded successfully")

async def main():
    """Main initialization function"""
    logger.info("Initializing authentication database...")
    
    try:
        await create_tables()
        await seed_test_data()
        logger.info("Database initialization completed successfully")
        
        # Display test credentials
        print("\n" + "="*50)
        print("🔐 TEST CREDENTIALS")
        print("="*50)
        print("Admin User:")
        print("  Username: admin")
        print("  Password: admin123")
        print("  Email: <EMAIL>")
        print()
        print("Test User:")
        print("  Username: testuser")
        print("  Password: test123")
        print("  Email: <EMAIL>")
        print()
        print("Demo User:")
        print("  Username: demo")
        print("  Password: demo123")
        print("  Email: <EMAIL>")
        print("="*50)
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())

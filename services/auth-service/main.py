# Authentication Service - User authentication with username/password and Google OAuth
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import RedirectResponse
from pydantic import BaseModel, EmailStr
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import jwt
import bcrypt
import httpx
import os
import logging
from contextlib import asynccontextmanager
import sqlite3
import aiosqlite
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Config:
    # JWT Configuration
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-super-secure-jwt-secret-key")
    JWT_ALGORITHM = "HS256"
    JWT_EXPIRATION_HOURS = int(os.getenv("JWT_EXPIRATION_HOURS", "24"))
    
    # Google OAuth Configuration
    GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
    GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
    GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost/api/auth/google/callback")
    
    # Database Configuration
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./auth.db")
    
    # Frontend URL for redirects
    FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost")

config = Config()

# Database setup
async def init_db():
    """Initialize the database with user tables"""
    db_path = Path("auth.db")
    
    async with aiosqlite.connect(db_path) as db:
        await db.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT,
                google_id TEXT UNIQUE,
                full_name TEXT,
                avatar_url TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        await db.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token_jti TEXT UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        await db.commit()
        logger.info("Database initialized successfully")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Auth Service starting up...")
    await init_db()
    logger.info("Auth Service ready")
    
    yield
    
    # Shutdown
    logger.info("Auth Service shutting down...")

app = FastAPI(
    title="RAG Authentication Service",
    description="Authentication service with username/password and Google OAuth",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

security = HTTPBearer(auto_error=False)

# Pydantic models
class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str]
    avatar_url: Optional[str]
    is_active: bool
    created_at: datetime

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: UserResponse

class GoogleAuthURL(BaseModel):
    auth_url: str

# Utility functions
def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """Verify a password against its hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def create_jwt_token(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a JWT token for a user"""
    now = datetime.now(timezone.utc)
    expires_at = now + timedelta(hours=config.JWT_EXPIRATION_HOURS)
    
    payload = {
        "sub": str(user_data["id"]),
        "username": user_data["username"],
        "email": user_data["email"],
        "iat": now,
        "exp": expires_at,
        "jti": f"user_{user_data['id']}_{int(now.timestamp())}"
    }
    
    token = jwt.encode(payload, config.JWT_SECRET_KEY, algorithm=config.JWT_ALGORITHM)
    
    return {
        "access_token": token,
        "token_type": "bearer",
        "expires_in": config.JWT_EXPIRATION_HOURS * 3600,
        "expires_at": expires_at
    }

async def get_user_by_username(username: str) -> Optional[Dict[str, Any]]:
    """Get user by username from database"""
    async with aiosqlite.connect("auth.db") as db:
        db.row_factory = aiosqlite.Row
        async with db.execute(
            "SELECT * FROM users WHERE username = ? AND is_active = TRUE", 
            (username,)
        ) as cursor:
            row = await cursor.fetchone()
            return dict(row) if row else None

async def get_user_by_email(email: str) -> Optional[Dict[str, Any]]:
    """Get user by email from database"""
    async with aiosqlite.connect("auth.db") as db:
        db.row_factory = aiosqlite.Row
        async with db.execute(
            "SELECT * FROM users WHERE email = ? AND is_active = TRUE", 
            (email,)
        ) as cursor:
            row = await cursor.fetchone()
            return dict(row) if row else None

async def get_user_by_google_id(google_id: str) -> Optional[Dict[str, Any]]:
    """Get user by Google ID from database"""
    async with aiosqlite.connect("auth.db") as db:
        db.row_factory = aiosqlite.Row
        async with db.execute(
            "SELECT * FROM users WHERE google_id = ? AND is_active = TRUE", 
            (google_id,)
        ) as cursor:
            row = await cursor.fetchone()
            return dict(row) if row else None

async def create_user(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new user in the database"""
    async with aiosqlite.connect("auth.db") as db:
        cursor = await db.execute("""
            INSERT INTO users (username, email, password_hash, google_id, full_name, avatar_url)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            user_data["username"],
            user_data["email"],
            user_data.get("password_hash"),
            user_data.get("google_id"),
            user_data.get("full_name"),
            user_data.get("avatar_url")
        ))
        
        user_id = cursor.lastrowid
        await db.commit()
        
        # Return the created user
        return await get_user_by_username(user_data["username"])

async def verify_jwt_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify and decode a JWT token"""
    try:
        payload = jwt.decode(token, config.JWT_SECRET_KEY, algorithms=[config.JWT_ALGORITHM])
        
        # Check if token is expired
        if datetime.fromtimestamp(payload["exp"], timezone.utc) < datetime.now(timezone.utc):
            return None
            
        return payload
    except jwt.InvalidTokenError:
        return None

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get current user from JWT token"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    payload = await verify_jwt_token(credentials.credentials)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    
    user = await get_user_by_username(payload["username"])
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    return user

# Authentication endpoints
@app.post("/register", response_model=TokenResponse)
async def register(user_data: UserCreate):
    """Register a new user with username and password"""
    # Check if user already exists
    existing_user = await get_user_by_username(user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )

    existing_email = await get_user_by_email(user_data.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create new user
    password_hash = hash_password(user_data.password)
    new_user_data = {
        "username": user_data.username,
        "email": user_data.email,
        "password_hash": password_hash,
        "full_name": user_data.full_name
    }

    user = await create_user(new_user_data)
    token_data = create_jwt_token(user)

    return TokenResponse(
        access_token=token_data["access_token"],
        token_type=token_data["token_type"],
        expires_in=token_data["expires_in"],
        user=UserResponse(**user)
    )

@app.post("/login", response_model=TokenResponse)
async def login(login_data: UserLogin):
    """Login with username and password"""
    user = await get_user_by_username(login_data.username)
    if not user or not user["password_hash"]:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password"
        )

    if not verify_password(login_data.password, user["password_hash"]):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid username or password"
        )

    token_data = create_jwt_token(user)

    return TokenResponse(
        access_token=token_data["access_token"],
        token_type=token_data["token_type"],
        expires_in=token_data["expires_in"],
        user=UserResponse(**user)
    )

@app.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get current user information"""
    return UserResponse(**current_user)

@app.post("/logout")
async def logout(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Logout user (invalidate token)"""
    # In a production system, you would add the token to a blacklist
    # For now, we'll just return success (client should delete the token)
    return {"message": "Successfully logged out"}

@app.get("/google/auth-url", response_model=GoogleAuthURL)
async def get_google_auth_url():
    """Get Google OAuth authorization URL"""
    if not config.GOOGLE_CLIENT_ID:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Google OAuth not configured"
        )

    auth_url = (
        f"https://accounts.google.com/o/oauth2/auth?"
        f"client_id={config.GOOGLE_CLIENT_ID}&"
        f"redirect_uri={config.GOOGLE_REDIRECT_URI}&"
        f"scope=openid email profile&"
        f"response_type=code&"
        f"access_type=offline"
    )

    return GoogleAuthURL(auth_url=auth_url)

@app.get("/google/callback")
async def google_callback(code: str, request: Request):
    """Handle Google OAuth callback"""
    if not config.GOOGLE_CLIENT_ID or not config.GOOGLE_CLIENT_SECRET:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Google OAuth not configured"
        )

    try:
        # Exchange code for access token
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://oauth2.googleapis.com/token",
                data={
                    "client_id": config.GOOGLE_CLIENT_ID,
                    "client_secret": config.GOOGLE_CLIENT_SECRET,
                    "code": code,
                    "grant_type": "authorization_code",
                    "redirect_uri": config.GOOGLE_REDIRECT_URI,
                }
            )

            if token_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to exchange code for token"
                )

            token_data = token_response.json()
            access_token = token_data["access_token"]

            # Get user info from Google
            user_response = await client.get(
                "https://www.googleapis.com/oauth2/v2/userinfo",
                headers={"Authorization": f"Bearer {access_token}"}
            )

            if user_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get user info from Google"
                )

            google_user = user_response.json()

            # Check if user exists by Google ID
            user = await get_user_by_google_id(google_user["id"])

            if not user:
                # Check if user exists by email
                user = await get_user_by_email(google_user["email"])

                if user:
                    # Update existing user with Google ID
                    async with aiosqlite.connect("auth.db") as db:
                        await db.execute(
                            "UPDATE users SET google_id = ?, avatar_url = ? WHERE id = ?",
                            (google_user["id"], google_user.get("picture"), user["id"])
                        )
                        await db.commit()

                    user = await get_user_by_email(google_user["email"])
                else:
                    # Create new user
                    new_user_data = {
                        "username": google_user["email"].split("@")[0],  # Use email prefix as username
                        "email": google_user["email"],
                        "google_id": google_user["id"],
                        "full_name": google_user.get("name"),
                        "avatar_url": google_user.get("picture")
                    }

                    # Ensure username is unique
                    counter = 1
                    original_username = new_user_data["username"]
                    while await get_user_by_username(new_user_data["username"]):
                        new_user_data["username"] = f"{original_username}{counter}"
                        counter += 1

                    user = await create_user(new_user_data)

            # Create JWT token
            token_data = create_jwt_token(user)

            # Redirect to frontend with token
            redirect_url = f"{config.FRONTEND_URL}?token={token_data['access_token']}"
            return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.error(f"Google OAuth error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )

# Health check
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "auth-service"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)

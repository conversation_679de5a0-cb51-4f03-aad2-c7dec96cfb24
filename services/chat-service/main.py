# Chat Service - Core RAG logic
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import httpx
import redis
import json
import uuid
import logging
from typing import Optional, List, Dict
from datetime import datetime
from qdrant_client import QdrantClient
from qdrant_client.http import models
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="RAG Chat Service", version="1.0.0")

class Config:
    EMBEDDING_SERVICE_URL = os.getenv("EMBEDDING_SERVICE_URL", "http://localhost:8003")
    LLM_SERVICE_URL = os.getenv("LLM_SERVICE_URL", "http://localhost:8004")
    QDRANT_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    RETRIEVAL_K = int(os.getenv("RETRIEVAL_K", "5"))
    COLLECTION_NAME = "documents"

config = Config()

try:
    qdrant_client = QdrantClient(url=config.QDRANT_URL)
    logger.info("Connected to Qdrant")
except Exception as e:
    logger.error(f"Failed to connect to Qdrant: {e}")
    qdrant_client = None

try:
    redis_client = redis.from_url(config.REDIS_URL, decode_responses=True)
    redis_client.ping()
    logger.info("Connected to Redis")
except Exception as e:
    logger.error(f"Failed to connect to Redis: {e}")
    redis_client = None

http_client = httpx.AsyncClient(timeout=60.0)

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    user_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    sources: Optional[List[Dict]] = None
    metadata: Optional[Dict] = None

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "qdrant": "healthy" if qdrant_client else "unhealthy",
            "redis": "healthy" if redis_client else "unhealthy"
        }
    }

async def retrieve_documents(query: str, k: int = None) -> List[Dict]:
    if not qdrant_client:
        return []
    
    k = k or config.RETRIEVAL_K
    
    try:
        # Get query embedding
        embedding_response = await http_client.post(
            f"{config.EMBEDDING_SERVICE_URL}/embed",
            json={"texts": [query]}
        )
        
        if embedding_response.status_code != 200:
            return []
        
        query_embedding = embedding_response.json()["embeddings"][0]
        
        # Search in Qdrant
        search_result = qdrant_client.search(
            collection_name=config.COLLECTION_NAME,
            query_vector=query_embedding,
            limit=k,
            with_payload=True
        )
        
        documents = []
        for result in search_result:
            documents.append({
                "content": result.payload.get("content", ""),
                "metadata": result.payload.get("metadata", {}),
                "score": result.score
            })
        
        return documents
    
    except Exception as e:
        logger.error(f"Error retrieving documents: {e}")
        return []

async def generate_response(prompt: str) -> str:
    try:
        llm_response = await http_client.post(
            f"{config.LLM_SERVICE_URL}/generate",
            json={"prompt": prompt, "max_tokens": 512, "temperature": 0.1}
        )
        
        if llm_response.status_code != 200:
            return "I apologize, but I'm having trouble generating a response right now."
        
        return llm_response.json()["text"]
    
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        return "I apologize, but I'm having trouble generating a response right now."

def build_rag_prompt(query: str, documents: List[Dict]) -> str:
    context_parts = []
    for i, doc in enumerate(documents[:config.RETRIEVAL_K]):
        context_parts.append(f"Document {i+1}:\n{doc['content']}\n")
    
    context = "\n".join(context_parts) if context_parts else "No relevant documents found."
    
    prompt = f"""Use the following context to answer the question. If the context doesn't contain relevant information, say so politely.

Context:
{context}

Question: {query}

Answer:"""
    
    return prompt

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    try:
        conversation_id = request.conversation_id or str(uuid.uuid4())
        
        # Retrieve relevant documents
        documents = await retrieve_documents(request.message)
        
        # Build RAG prompt
        prompt = build_rag_prompt(request.message, documents)
        
        # Generate response
        response_text = await generate_response(prompt)
        
        # Format sources
        sources = []
        for doc in documents:
            sources.append({
                "content": doc["content"][:200] + "..." if len(doc["content"]) > 200 else doc["content"],
                "metadata": doc["metadata"],
                "relevance_score": doc["score"]
            })
        
        return ChatResponse(
            response=response_text,
            conversation_id=conversation_id,
            sources=sources,
            metadata={"sources_count": len(sources)}
        )
    
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")

@app.on_event("startup")
async def startup_event():
    if qdrant_client:
        try:
            collections = qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if config.COLLECTION_NAME not in collection_names:
                qdrant_client.create_collection(
                    collection_name=config.COLLECTION_NAME,
                    vectors_config=models.VectorParams(
                        size=384,
                        distance=models.Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {config.COLLECTION_NAME}")
        except Exception as e:
            logger.error(f"Error setting up Qdrant: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)

version: '3.8'

services:
  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Embedding service with sentence-transformers (default)
  embedding-service:
    build: .
    ports:
      - "8003:8003"
    environment:
      - MODEL_PROVIDER=sentence_transformers
      - MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
      - DEVICE=cpu
      - REDIS_URL=redis://redis:6379
      - BATCH_SIZE=32
      - CACHE_TTL=3600
    volumes:
      - model_cache:/app/models
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Alternative: OpenAI-based service
  embedding-service-openai:
    build: .
    ports:
      - "8004:8003"
    environment:
      - MODEL_PROVIDER=openai
      - MODEL_NAME=text-embedding-ada-002
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - REDIS_URL=redis://redis:6379
      - BATCH_SIZE=32
      - CACHE_TTL=3600
    depends_on:
      redis:
        condition: service_healthy
    profiles:
      - openai
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Alternative: Local model service
  embedding-service-local:
    build: .
    ports:
      - "8005:8003"
    environment:
      - MODEL_PROVIDER=local
      - MODEL_NAME=llama3.2:3b
      - LOCAL_MODEL_URL=http://host.docker.internal:11434
      - REDIS_URL=redis://redis:6379
      - BATCH_SIZE=32
      - CACHE_TTL=3600
    depends_on:
      redis:
        condition: service_healthy
    profiles:
      - local
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
  model_cache:

# Embedding Service Configuration Examples

# ===== PROVIDER SELECTION =====
# Choose one: sentence_transformers, openai, anthropic, local
MODEL_PROVIDER=sentence_transformers

# ===== SENTENCE TRANSFORMERS CONFIGURATION =====
# For local sentence-transformers models
MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
# Alternative models:
# MODEL_NAME=sentence-transformers/all-mpnet-base-v2
# MODEL_NAME=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
DEVICE=cpu
# DEVICE=cuda  # Use for GPU acceleration

# ===== OPENAI CONFIGURATION =====
# For OpenAI embedding models
# MODEL_PROVIDER=openai
# MODEL_NAME=text-embedding-ada-002
# MODEL_NAME=text-embedding-3-small
# MODEL_NAME=text-embedding-3-large
# OPENAI_API_KEY=your_openai_api_key_here

# ===== ANTHROPIC CONFIGURATION =====
# Note: Anthropic doesn't currently provide embedding models
# This is a placeholder for future support
# MODEL_PROVIDER=anthropic
# MODEL_NAME=claude-3-haiku
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ===== LOCAL MODEL CONFIGURATION =====
# For locally deployed models (GPT-OSS, Llama, etc.)
# MODEL_PROVIDER=local
# MODEL_NAME=gpt-oss:20b
# MODEL_NAME=llama3.2:3b
# LOCAL_MODEL_URL=http://localhost:8080
# LOCAL_MODEL_API_KEY=optional_api_key

# ===== GENERAL CONFIGURATION =====
BATCH_SIZE=32
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# ===== EXAMPLE CONFIGURATIONS =====

# Example 1: OpenAI GPT-4 Embeddings
# MODEL_PROVIDER=openai
# MODEL_NAME=text-embedding-3-large
# OPENAI_API_KEY=sk-your-key-here

# Example 2: Local Llama Model
# MODEL_PROVIDER=local
# MODEL_NAME=llama3.2:3b
# LOCAL_MODEL_URL=http://localhost:11434
# LOCAL_MODEL_API_KEY=optional

# Example 3: Local GPT-OSS Model
# MODEL_PROVIDER=local
# MODEL_NAME=gpt-oss:20b
# LOCAL_MODEL_URL=http://localhost:8080
# LOCAL_MODEL_API_KEY=your_local_api_key

# Example 4: High-quality sentence transformer
# MODEL_PROVIDER=sentence_transformers
# MODEL_NAME=sentence-transformers/all-mpnet-base-v2
# DEVICE=cuda

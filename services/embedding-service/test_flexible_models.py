#!/usr/bin/env python3
"""
Test script for the flexible embedding service
Demonstrates switching between different model providers
"""

import requests
import json
import time
import os
from typing import List, Dict, Any

class EmbeddingServiceTester:
    def __init__(self, base_url: str = "http://localhost:8003"):
        self.base_url = base_url
        
    def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        response = requests.get(f"{self.base_url}/health")
        return response.json()
    
    def list_models(self) -> Dict[str, Any]:
        """List currently loaded models"""
        response = requests.get(f"{self.base_url}/models")
        return response.json()
    
    def switch_model(self, provider: str, model_name: str, **kwargs) -> Dict[str, Any]:
        """Switch to a different model"""
        payload = {
            "provider": provider,
            "model_name": model_name,
            **kwargs
        }
        response = requests.post(f"{self.base_url}/switch-model", json=payload)
        return response.json()
    
    def generate_embeddings(self, texts: List[str], model_name: str = None) -> Dict[str, Any]:
        """Generate embeddings for texts"""
        payload = {
            "texts": texts,
            "normalize": True
        }
        if model_name:
            payload["model_name"] = model_name
            
        response = requests.post(f"{self.base_url}/embed", json=payload)
        return response.json()
    
    def test_model(self, provider: str, model_name: str, test_texts: List[str], **kwargs):
        """Test a specific model configuration"""
        print(f"\n{'='*60}")
        print(f"Testing {provider}:{model_name}")
        print(f"{'='*60}")
        
        try:
            # Switch to the model
            print("Switching model...")
            switch_result = self.switch_model(provider, model_name, **kwargs)
            print(f"✓ Model switched: {switch_result.get('message', 'Success')}")
            
            # Generate embeddings
            print("Generating embeddings...")
            start_time = time.time()
            result = self.generate_embeddings(test_texts)
            end_time = time.time()
            
            model_info = result.get('model_info', {})
            embeddings = result.get('embeddings', [])
            
            print(f"✓ Generated embeddings for {len(embeddings)} texts")
            print(f"✓ Model: {model_info.get('model_name')}")
            print(f"✓ Provider: {model_info.get('provider')}")
            print(f"✓ Dimension: {model_info.get('dimension')}")
            print(f"✓ Processing time: {result.get('processing_time', 0):.3f}s")
            print(f"✓ Total time: {end_time - start_time:.3f}s")
            
            # Show sample embedding
            if embeddings:
                sample_embedding = embeddings[0][:5]  # First 5 dimensions
                print(f"✓ Sample embedding: {sample_embedding}...")
            
            return True
            
        except Exception as e:
            print(f"✗ Error testing {provider}:{model_name}: {e}")
            return False

def main():
    """Main test function"""
    tester = EmbeddingServiceTester()
    
    # Test texts
    test_texts = [
        "Hello, how are you today?",
        "The quick brown fox jumps over the lazy dog.",
        "Machine learning is a subset of artificial intelligence."
    ]
    
    print("Flexible Embedding Service Test")
    print("=" * 60)
    
    # Check service health
    try:
        health = tester.health_check()
        print(f"Service Status: {health.get('status')}")
        print(f"Current Provider: {health.get('current_provider')}")
        print(f"Available Providers: {health.get('available_providers')}")
    except Exception as e:
        print(f"✗ Service not available: {e}")
        return
    
    # Test configurations
    test_configs = [
        {
            "provider": "sentence_transformers",
            "model_name": "sentence-transformers/all-MiniLM-L6-v2",
            "description": "Default sentence transformer model"
        },
        {
            "provider": "sentence_transformers", 
            "model_name": "sentence-transformers/all-mpnet-base-v2",
            "description": "Higher quality sentence transformer model"
        }
    ]
    
    # Add OpenAI test if API key is available
    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key:
        test_configs.extend([
            {
                "provider": "openai",
                "model_name": "text-embedding-ada-002",
                "api_key": openai_key,
                "description": "OpenAI Ada embedding model"
            },
            {
                "provider": "openai",
                "model_name": "text-embedding-3-small",
                "api_key": openai_key,
                "description": "OpenAI v3 small embedding model"
            }
        ])
    else:
        print("\nNote: Set OPENAI_API_KEY environment variable to test OpenAI models")
    
    # Add local model test if URL is available
    local_url = os.getenv("LOCAL_MODEL_URL", "http://localhost:11434")
    if local_url != "http://localhost:8080":  # Default changed, likely configured
        test_configs.append({
            "provider": "local",
            "model_name": "llama3.2:3b",
            "base_url": local_url,
            "description": "Local Llama model"
        })
    else:
        print("\nNote: Set LOCAL_MODEL_URL environment variable to test local models")
    
    # Run tests
    successful_tests = 0
    total_tests = len(test_configs)
    
    for config in test_configs:
        description = config.pop("description", "")
        print(f"\n{description}")
        
        success = tester.test_model(
            config["provider"],
            config["model_name"],
            test_texts,
            **{k: v for k, v in config.items() if k not in ["provider", "model_name"]}
        )
        
        if success:
            successful_tests += 1
    
    # Summary
    print(f"\n{'='*60}")
    print(f"Test Summary: {successful_tests}/{total_tests} tests passed")
    print(f"{'='*60}")
    
    # Show final model status
    try:
        models = tester.list_models()
        print(f"\nLoaded models: {len(models.get('loaded_models', {}))}")
        for model_key, model_info in models.get('loaded_models', {}).items():
            print(f"  - {model_key}: {model_info.get('dimension')} dimensions")
    except Exception as e:
        print(f"Could not list models: {e}")

if __name__ == "__main__":
    main()

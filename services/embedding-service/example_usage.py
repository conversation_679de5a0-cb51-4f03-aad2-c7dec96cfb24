#!/usr/bin/env python3
"""
Example usage of the flexible embedding service
Shows how to switch between different models dynamically
"""

import requests
import json
import os

# Service URL
SERVICE_URL = "http://localhost:8003"

def example_sentence_transformers():
    """Example using sentence-transformers models"""
    print("\n=== Sentence Transformers Example ===")
    
    # Switch to a sentence transformer model
    switch_response = requests.post(f"{SERVICE_URL}/switch-model", json={
        "provider": "sentence_transformers",
        "model_name": "sentence-transformers/all-MiniLM-L6-v2"
    })
    print(f"Model switch: {switch_response.json()}")
    
    # Generate embeddings
    embed_response = requests.post(f"{SERVICE_URL}/embed", json={
        "texts": [
            "Hello world",
            "How are you today?",
            "The weather is nice"
        ],
        "normalize": True
    })
    
    result = embed_response.json()
    print(f"Generated {len(result['embeddings'])} embeddings")
    print(f"Model info: {result['model_info']}")
    print(f"Processing time: {result['processing_time']:.3f}s")

def example_openai():
    """Example using OpenAI models"""
    print("\n=== OpenAI Example ===")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("Skipping OpenAI example - no API key found")
        print("Set OPENAI_API_KEY environment variable to test")
        return
    
    # Switch to OpenAI model
    switch_response = requests.post(f"{SERVICE_URL}/switch-model", json={
        "provider": "openai",
        "model_name": "text-embedding-ada-002",
        "api_key": api_key
    })
    print(f"Model switch: {switch_response.json()}")
    
    # Generate embeddings
    embed_response = requests.post(f"{SERVICE_URL}/embed", json={
        "texts": [
            "Machine learning is fascinating",
            "Natural language processing",
            "Artificial intelligence applications"
        ],
        "normalize": True
    })
    
    result = embed_response.json()
    print(f"Generated {len(result['embeddings'])} embeddings")
    print(f"Model info: {result['model_info']}")
    print(f"Processing time: {result['processing_time']:.3f}s")

def example_local_model():
    """Example using local models (Ollama/Llama)"""
    print("\n=== Local Model Example ===")
    
    local_url = os.getenv("LOCAL_MODEL_URL", "http://localhost:11434")
    
    # Switch to local model
    switch_response = requests.post(f"{SERVICE_URL}/switch-model", json={
        "provider": "local",
        "model_name": "llama3.2:3b",
        "base_url": local_url
    })
    
    if switch_response.status_code != 200:
        print("Skipping local model example - model not available")
        print(f"Make sure your local model is running at {local_url}")
        return
    
    print(f"Model switch: {switch_response.json()}")
    
    # Generate embeddings
    embed_response = requests.post(f"{SERVICE_URL}/embed", json={
        "texts": [
            "Local models are powerful",
            "Running AI on your own hardware",
            "Privacy-focused machine learning"
        ],
        "normalize": True
    })
    
    result = embed_response.json()
    print(f"Generated {len(result['embeddings'])} embeddings")
    print(f"Model info: {result['model_info']}")
    print(f"Processing time: {result['processing_time']:.3f}s")

def example_per_request_model():
    """Example using different models per request"""
    print("\n=== Per-Request Model Selection ===")
    
    # First, make sure we have multiple models loaded
    requests.post(f"{SERVICE_URL}/switch-model", json={
        "provider": "sentence_transformers",
        "model_name": "sentence-transformers/all-MiniLM-L6-v2"
    })
    
    requests.post(f"{SERVICE_URL}/switch-model", json={
        "provider": "sentence_transformers", 
        "model_name": "sentence-transformers/all-mpnet-base-v2"
    })
    
    # Use different models for different requests
    text = "This is a test sentence for embedding"
    
    # Request 1: Use MiniLM model
    response1 = requests.post(f"{SERVICE_URL}/embed", json={
        "texts": [text],
        "model_name": "sentence-transformers/all-MiniLM-L6-v2"
    })
    
    # Request 2: Use MPNet model  
    response2 = requests.post(f"{SERVICE_URL}/embed", json={
        "texts": [text],
        "model_name": "sentence-transformers/all-mpnet-base-v2"
    })
    
    result1 = response1.json()
    result2 = response2.json()
    
    print(f"Model 1: {result1['model_info']['model_name']}")
    print(f"  Dimension: {result1['model_info']['dimension']}")
    print(f"  Processing time: {result1['processing_time']:.3f}s")
    
    print(f"Model 2: {result2['model_info']['model_name']}")
    print(f"  Dimension: {result2['model_info']['dimension']}")
    print(f"  Processing time: {result2['processing_time']:.3f}s")

def check_service_status():
    """Check if the service is running and show status"""
    print("=== Service Status ===")
    
    try:
        health_response = requests.get(f"{SERVICE_URL}/health")
        health = health_response.json()
        
        print(f"Status: {health['status']}")
        print(f"Current provider: {health['current_provider']}")
        print(f"Available providers: {health['available_providers']}")
        
        models_response = requests.get(f"{SERVICE_URL}/models")
        models = models_response.json()
        
        print(f"Default model: {models['default_model']}")
        print(f"Loaded models: {len(models['loaded_models'])}")
        
        for model_key, model_info in models['loaded_models'].items():
            print(f"  - {model_key}: {model_info['dimension']} dimensions")
        
        return True
        
    except Exception as e:
        print(f"Service not available: {e}")
        print("Make sure the embedding service is running on port 8003")
        return False

def main():
    """Run all examples"""
    print("Flexible Embedding Service - Usage Examples")
    print("=" * 50)
    
    # Check if service is running
    if not check_service_status():
        return
    
    # Run examples
    example_sentence_transformers()
    example_openai()
    example_local_model()
    example_per_request_model()
    
    print("\n=== Final Status ===")
    check_service_status()

if __name__ == "__main__":
    main()

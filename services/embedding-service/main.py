# Embedding Service - Text embedding generation with flexible model support
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
import os
import redis
import json
import hashlib
import asyncio
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor
from abc import ABC, abstractmethod

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Embedding Service starting up...")
    try:
        await embedding_manager.load_model()
        logger.info("Default model loaded successfully on startup")
    except Exception as e:
        logger.warning(f"Failed to load default model on startup: {e}")

    yield

    # Shutdown
    logger.info("Embedding Service shutting down...")
    for provider in embedding_manager.providers.values():
        if hasattr(provider, 'close'):
            await provider.close()

app = FastAPI(title="RAG Embedding Service", version="2.0.0", lifespan=lifespan)

class Config:
    # Model configuration
    MODEL_PROVIDER = os.getenv("MODEL_PROVIDER", "sentence_transformers")  # sentence_transformers, openai, anthropic, local
    MODEL_NAME = os.getenv("MODEL_NAME", "sentence-transformers/all-MiniLM-L6-v2")

    # API Keys for external providers
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")

    # Local model configuration
    LOCAL_MODEL_URL = os.getenv("LOCAL_MODEL_URL", "http://localhost:8080")
    LOCAL_MODEL_API_KEY = os.getenv("LOCAL_MODEL_API_KEY")

    # General configuration
    DEVICE = os.getenv("DEVICE", "cpu")
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "32"))
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    CACHE_TTL = int(os.getenv("CACHE_TTL", "3600"))
    MODEL_CACHE_DIR = "/app/models"

config = Config()
os.makedirs(config.MODEL_CACHE_DIR, exist_ok=True)

try:
    redis_client = redis.from_url(config.REDIS_URL, decode_responses=True)
    redis_client.ping()
    logger.info("Connected to Redis")
except Exception as e:
    logger.error(f"Failed to connect to Redis: {e}")
    redis_client = None

thread_pool = ThreadPoolExecutor(max_workers=4)

# Abstract base class for embedding providers
class EmbeddingProvider(ABC):
    """Abstract base class for different embedding model providers"""

    def __init__(self, model_name: str, **kwargs):
        self.model_name = model_name
        self.is_loaded = False
        self.dimension = None

    @abstractmethod
    async def load_model(self):
        """Load the embedding model"""
        pass

    @abstractmethod
    async def generate_embeddings(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        """Generate embeddings for a list of texts"""
        pass

    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the model"""
        pass

# SentenceTransformers Provider
class SentenceTransformersProvider(EmbeddingProvider):
    """Provider for sentence-transformers models"""

    def __init__(self, model_name: str, device: str = "cpu", cache_dir: str = None):
        super().__init__(model_name)
        self.device = device
        self.cache_dir = cache_dir
        self.model = None

    async def load_model(self):
        if self.is_loaded:
            return

        try:
            from sentence_transformers import SentenceTransformer
            logger.info(f"Loading SentenceTransformers model: {self.model_name}")

            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                thread_pool,
                self._load_model_sync
            )

            # Get dimension
            test_embedding = self.model.encode(["test"], show_progress_bar=False)
            self.dimension = test_embedding.shape[1]

            self.is_loaded = True
            logger.info(f"SentenceTransformers model loaded. Dimension: {self.dimension}")

        except Exception as e:
            logger.error(f"Error loading SentenceTransformers model: {e}")
            raise

    def _load_model_sync(self):
        from sentence_transformers import SentenceTransformer
        return SentenceTransformer(
            self.model_name,
            cache_folder=self.cache_dir,
            device=self.device
        )

    async def generate_embeddings(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        if not self.is_loaded:
            await self.load_model()

        loop = asyncio.get_event_loop()
        embeddings = await loop.run_in_executor(
            thread_pool,
            self._generate_batch_embeddings,
            texts,
            normalize
        )

        return [embedding.tolist() for embedding in embeddings]

    def _generate_batch_embeddings(self, texts: List[str], normalize: bool):
        return self.model.encode(
            texts,
            batch_size=len(texts),
            show_progress_bar=False,
            normalize_embeddings=normalize,
            convert_to_tensor=False
        )

    def get_model_info(self) -> Dict[str, Any]:
        return {
            "provider": "sentence_transformers",
            "model_name": self.model_name,
            "device": self.device,
            "dimension": self.dimension,
            "is_loaded": self.is_loaded
        }

# OpenAI Provider
class OpenAIProvider(EmbeddingProvider):
    """Provider for OpenAI embedding models"""

    def __init__(self, model_name: str, api_key: str):
        super().__init__(model_name)
        self.api_key = api_key
        self.client = None

    async def load_model(self):
        if self.is_loaded:
            return

        try:
            import openai
            logger.info(f"Initializing OpenAI client for model: {self.model_name}")

            self.client = openai.AsyncOpenAI(api_key=self.api_key)

            # Test the connection and get dimension
            test_response = await self.client.embeddings.create(
                model=self.model_name,
                input=["test"]
            )
            self.dimension = len(test_response.data[0].embedding)

            self.is_loaded = True
            logger.info(f"OpenAI model initialized. Dimension: {self.dimension}")

        except Exception as e:
            logger.error(f"Error initializing OpenAI model: {e}")
            raise

    async def generate_embeddings(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        if not self.is_loaded:
            await self.load_model()

        try:
            response = await self.client.embeddings.create(
                model=self.model_name,
                input=texts
            )

            embeddings = [data.embedding for data in response.data]

            if normalize:
                # Normalize embeddings if requested
                import numpy as np
                embeddings = [
                    (np.array(emb) / np.linalg.norm(emb)).tolist()
                    for emb in embeddings
                ]

            return embeddings

        except Exception as e:
            logger.error(f"Error generating OpenAI embeddings: {e}")
            raise

    def get_model_info(self) -> Dict[str, Any]:
        return {
            "provider": "openai",
            "model_name": self.model_name,
            "dimension": self.dimension,
            "is_loaded": self.is_loaded
        }

# Anthropic Provider (Note: Anthropic doesn't have embedding models, but keeping for future)
class AnthropicProvider(EmbeddingProvider):
    """Provider for Anthropic models (placeholder for future embedding support)"""

    def __init__(self, model_name: str, api_key: str):
        super().__init__(model_name)
        self.api_key = api_key

    async def load_model(self):
        raise NotImplementedError("Anthropic doesn't currently provide embedding models")

    async def generate_embeddings(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        raise NotImplementedError("Anthropic doesn't currently provide embedding models")

    def get_model_info(self) -> Dict[str, Any]:
        return {
            "provider": "anthropic",
            "model_name": self.model_name,
            "dimension": None,
            "is_loaded": False,
            "note": "Anthropic doesn't currently provide embedding models"
        }

# Local Model Provider (for local deployments like GPT-OSS, Llama, etc.)
class LocalModelProvider(EmbeddingProvider):
    """Provider for locally deployed models"""

    def __init__(self, model_name: str, base_url: str, api_key: str = None):
        super().__init__(model_name)
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = None

    async def load_model(self):
        if self.is_loaded:
            return

        try:
            import aiohttp
            logger.info(f"Initializing local model client for: {self.model_name} at {self.base_url}")

            self.session = aiohttp.ClientSession()

            # Test the connection and get dimension
            test_embeddings = await self._make_request(["test"])
            if test_embeddings:
                self.dimension = len(test_embeddings[0])
                self.is_loaded = True
                logger.info(f"Local model initialized. Dimension: {self.dimension}")
            else:
                raise Exception("Failed to get test embedding from local model")

        except Exception as e:
            logger.error(f"Error initializing local model: {e}")
            raise

    async def _make_request(self, texts: List[str]) -> List[List[float]]:
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        # Try different API formats that local models might use
        payload_formats = [
            # OpenAI-compatible format
            {
                "model": self.model_name,
                "input": texts
            },
            # Alternative format
            {
                "texts": texts,
                "model": self.model_name
            },
            # Simple format
            {
                "input": texts
            }
        ]

        endpoints = ["/v1/embeddings", "/embeddings", "/embed"]

        for endpoint in endpoints:
            for payload in payload_formats:
                try:
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=payload,
                        headers=headers
                    ) as response:
                        if response.status == 200:
                            data = await response.json()

                            # Try to extract embeddings from different response formats
                            if "data" in data and isinstance(data["data"], list):
                                return [item["embedding"] for item in data["data"]]
                            elif "embeddings" in data:
                                return data["embeddings"]
                            elif isinstance(data, list):
                                return data

                except Exception as e:
                    logger.debug(f"Failed {endpoint} with payload format: {e}")
                    continue

        raise Exception("Failed to get embeddings from local model with any known format")

    async def generate_embeddings(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        if not self.is_loaded:
            await self.load_model()

        try:
            embeddings = await self._make_request(texts)

            if normalize:
                # Normalize embeddings if requested
                import numpy as np
                embeddings = [
                    (np.array(emb) / np.linalg.norm(emb)).tolist()
                    for emb in embeddings
                ]

            return embeddings

        except Exception as e:
            logger.error(f"Error generating local model embeddings: {e}")
            raise

    def get_model_info(self) -> Dict[str, Any]:
        return {
            "provider": "local",
            "model_name": self.model_name,
            "base_url": self.base_url,
            "dimension": self.dimension,
            "is_loaded": self.is_loaded
        }

    async def close(self):
        if self.session:
            await self.session.close()

# Provider Factory
def create_embedding_provider(provider_type: str, model_name: str, **kwargs) -> EmbeddingProvider:
    """Factory function to create embedding providers"""

    if provider_type == "sentence_transformers":
        return SentenceTransformersProvider(
            model_name=model_name,
            device=kwargs.get("device", "cpu"),
            cache_dir=kwargs.get("cache_dir")
        )
    elif provider_type == "openai":
        api_key = kwargs.get("api_key")
        if not api_key:
            raise ValueError("OpenAI API key is required")
        return OpenAIProvider(model_name=model_name, api_key=api_key)
    elif provider_type == "anthropic":
        api_key = kwargs.get("api_key")
        if not api_key:
            raise ValueError("Anthropic API key is required")
        return AnthropicProvider(model_name=model_name, api_key=api_key)
    elif provider_type == "local":
        base_url = kwargs.get("base_url")
        if not base_url:
            raise ValueError("Base URL is required for local models")
        return LocalModelProvider(
            model_name=model_name,
            base_url=base_url,
            api_key=kwargs.get("api_key")
        )
    else:
        raise ValueError(f"Unknown provider type: {provider_type}")

class EmbeddingRequest(BaseModel):
    texts: List[str]
    normalize: Optional[bool] = True
    model_name: Optional[str] = None  # Allow overriding model per request

class EmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    model_info: Dict[str, Any]
    processing_time: float

class EmbeddingRequest(BaseModel):
    texts: List[str]
    normalize: Optional[bool] = True

class EmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    model_name: str
    dimension: int
    processing_time: float

class EmbeddingManager:
    def __init__(self):
        self.providers = {}  # Cache for different providers
        self.default_provider = None
        self._initialize_default_provider()

    def _initialize_default_provider(self):
        """Initialize the default provider based on config"""
        try:
            provider_kwargs = {
                "device": config.DEVICE,
                "cache_dir": config.MODEL_CACHE_DIR,
                "api_key": None,
                "base_url": None
            }

            if config.MODEL_PROVIDER == "openai":
                provider_kwargs["api_key"] = config.OPENAI_API_KEY
            elif config.MODEL_PROVIDER == "anthropic":
                provider_kwargs["api_key"] = config.ANTHROPIC_API_KEY
            elif config.MODEL_PROVIDER == "local":
                provider_kwargs["base_url"] = config.LOCAL_MODEL_URL
                provider_kwargs["api_key"] = config.LOCAL_MODEL_API_KEY

            self.default_provider = create_embedding_provider(
                config.MODEL_PROVIDER,
                config.MODEL_NAME,
                **provider_kwargs
            )

            # Cache the default provider
            provider_key = f"{config.MODEL_PROVIDER}:{config.MODEL_NAME}"
            self.providers[provider_key] = self.default_provider

        except Exception as e:
            logger.error(f"Error initializing default provider: {e}")
            raise

    async def get_provider(self, model_name: str = None) -> EmbeddingProvider:
        """Get or create a provider for the specified model"""
        if not model_name:
            return self.default_provider

        # Check if we already have this provider
        provider_key = f"{config.MODEL_PROVIDER}:{model_name}"
        if provider_key in self.providers:
            return self.providers[provider_key]

        # Create new provider for different model
        try:
            provider_kwargs = {
                "device": config.DEVICE,
                "cache_dir": config.MODEL_CACHE_DIR,
                "api_key": None,
                "base_url": None
            }

            if config.MODEL_PROVIDER == "openai":
                provider_kwargs["api_key"] = config.OPENAI_API_KEY
            elif config.MODEL_PROVIDER == "anthropic":
                provider_kwargs["api_key"] = config.ANTHROPIC_API_KEY
            elif config.MODEL_PROVIDER == "local":
                provider_kwargs["base_url"] = config.LOCAL_MODEL_URL
                provider_kwargs["api_key"] = config.LOCAL_MODEL_API_KEY

            provider = create_embedding_provider(
                config.MODEL_PROVIDER,
                model_name,
                **provider_kwargs
            )

            self.providers[provider_key] = provider
            return provider

        except Exception as e:
            logger.error(f"Error creating provider for {model_name}: {e}")
            raise

    @property
    def is_loaded(self):
        return self.default_provider.is_loaded if self.default_provider else False

    @property
    def model_name(self):
        return self.default_provider.model_name if self.default_provider else None

    @property
    def dimension(self):
        return self.default_provider.dimension if self.default_provider else None

    async def load_model(self, model_name: str = None):
        """Load the specified model or default model"""
        provider = await self.get_provider(model_name)
        await provider.load_model()

    def _get_cache_key(self, text: str, model_name: str) -> str:
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"embedding:{model_name}:{text_hash}"

    async def get_cached_embedding(self, text: str, model_name: str) -> Optional[List[float]]:
        if not redis_client:
            return None

        try:
            cache_key = self._get_cache_key(text, model_name)
            cached_data = redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval error: {e}")
            return None

    async def cache_embedding(self, text: str, embedding: List[float], model_name: str):
        if not redis_client:
            return

        try:
            cache_key = self._get_cache_key(text, model_name)
            redis_client.setex(
                cache_key,
                config.CACHE_TTL,
                json.dumps(embedding)
            )
        except Exception as e:
            logger.error(f"Cache storage error: {e}")

    async def generate_embeddings(self, texts: List[str], normalize: bool = True, model_name: str = None) -> List[List[float]]:
        provider = await self.get_provider(model_name)

        all_embeddings = []
        uncached_texts = []
        uncached_indices = []
        cached_embeddings = {}

        # Check cache
        for i, text in enumerate(texts):
            cached_embedding = await self.get_cached_embedding(text, provider.model_name)
            if cached_embedding:
                cached_embeddings[i] = cached_embedding
            else:
                uncached_texts.append(text)
                uncached_indices.append(i)

        # Generate new embeddings
        new_embeddings = {}
        if uncached_texts:
            logger.info(f"Generating embeddings for {len(uncached_texts)} texts using {provider.model_name}")

            batch_embeddings = await provider.generate_embeddings(uncached_texts, normalize)

            for j, (text, embedding) in enumerate(zip(uncached_texts, batch_embeddings)):
                original_index = uncached_indices[j]
                new_embeddings[original_index] = embedding

                # Cache the embedding
                await self.cache_embedding(text, embedding, provider.model_name)

        # Combine results
        for i in range(len(texts)):
            if i in cached_embeddings:
                all_embeddings.append(cached_embeddings[i])
            else:
                all_embeddings.append(new_embeddings[i])

        return all_embeddings

embedding_manager = EmbeddingManager()

@app.get("/health")
async def health_check():
    from datetime import timezone
    return {
        "status": "healthy" if embedding_manager.is_loaded else "loading",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "model_info": embedding_manager.default_provider.get_model_info() if embedding_manager.default_provider else None,
        "available_providers": ["sentence_transformers", "openai", "local"],
        "current_provider": config.MODEL_PROVIDER
    }

@app.get("/models")
async def list_models():
    """List available models and their status"""
    models_info = {}
    for provider_key, provider in embedding_manager.providers.items():
        models_info[provider_key] = provider.get_model_info()

    return {
        "default_model": config.MODEL_NAME,
        "default_provider": config.MODEL_PROVIDER,
        "loaded_models": models_info
    }

@app.post("/embed", response_model=EmbeddingResponse)
async def generate_embeddings(request: EmbeddingRequest):
    try:
        from datetime import timezone
        start_time = datetime.now(timezone.utc)

        if not request.texts:
            raise HTTPException(status_code=400, detail="No texts provided")

        # Use specified model or default
        model_name = request.model_name if request.model_name else None
        provider = await embedding_manager.get_provider(model_name)

        embeddings = await embedding_manager.generate_embeddings(
            request.texts,
            normalize=request.normalize,
            model_name=model_name
        )

        end_time = datetime.now(timezone.utc)
        processing_time = (end_time - start_time).total_seconds()

        return EmbeddingResponse(
            embeddings=embeddings,
            model_info=provider.get_model_info(),
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"Embedding generation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/switch-model")
async def switch_model(
    provider: str,
    model_name: str,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None
):
    """Switch to a different model/provider"""
    try:
        provider_kwargs = {
            "device": config.DEVICE,
            "cache_dir": config.MODEL_CACHE_DIR,
            "api_key": api_key,
            "base_url": base_url
        }

        # Create new provider
        new_provider = create_embedding_provider(provider, model_name, **provider_kwargs)
        await new_provider.load_model()

        # Cache the new provider
        provider_key = f"{provider}:{model_name}"
        embedding_manager.providers[provider_key] = new_provider

        return {
            "message": f"Successfully switched to {provider}:{model_name}",
            "model_info": new_provider.get_model_info()
        }

    except Exception as e:
        logger.error(f"Error switching model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Lifespan is already defined above

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)

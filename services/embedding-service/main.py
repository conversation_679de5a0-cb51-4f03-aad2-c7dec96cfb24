# Embedding Service - Text embedding generation
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
import logging
from typing import List, Optional
from datetime import datetime
import os
import redis
import json
import hashlib
import asyncio
from concurrent.futures import ThreadPoolExecutor
from sentence_transformers import SentenceTransformer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="RAG Embedding Service", version="1.0.0")

class Config:
    MODEL_NAME = os.getenv("MODEL_NAME", "sentence-transformers/all-MiniLM-L6-v2")
    DEVICE = os.getenv("DEVICE", "cpu")
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "32"))
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    CACHE_TTL = int(os.getenv("CACHE_TTL", "3600"))
    MODEL_CACHE_DIR = "/app/models"

config = Config()
os.makedirs(config.MODEL_CACHE_DIR, exist_ok=True)

try:
    redis_client = redis.from_url(config.REDIS_URL, decode_responses=True)
    redis_client.ping()
    logger.info("Connected to Redis")
except Exception as e:
    logger.error(f"Failed to connect to Redis: {e}")
    redis_client = None

thread_pool = ThreadPoolExecutor(max_workers=4)

class EmbeddingRequest(BaseModel):
    texts: List[str]
    normalize: Optional[bool] = True

class EmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    model_name: str
    dimension: int
    processing_time: float

class EmbeddingManager:
    def __init__(self):
        self.model = None
        self.model_name = config.MODEL_NAME
        self.is_loaded = False
        self.dimension = None
        
    async def load_model(self):
        if self.is_loaded:
            return
        
        try:
            logger.info(f"Loading model: {self.model_name}")
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                thread_pool,
                self._load_model_sync
            )
            
            # Get dimension
            test_embedding = self.model.encode(["test"], show_progress_bar=False)
            self.dimension = test_embedding.shape[1]
            
            self.is_loaded = True
            logger.info(f"Model loaded. Dimension: {self.dimension}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def _load_model_sync(self):
        return SentenceTransformer(
            self.model_name,
            cache_folder=config.MODEL_CACHE_DIR,
            device=config.DEVICE
        )
    
    def _get_cache_key(self, text: str) -> str:
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"embedding:{self.model_name}:{text_hash}"
    
    async def get_cached_embedding(self, text: str) -> Optional[List[float]]:
        if not redis_client:
            return None
        
        try:
            cache_key = self._get_cache_key(text)
            cached_data = redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval error: {e}")
            return None
    
    async def cache_embedding(self, text: str, embedding: List[float]):
        if not redis_client:
            return
        
        try:
            cache_key = self._get_cache_key(text)
            redis_client.setex(
                cache_key,
                config.CACHE_TTL,
                json.dumps(embedding)
            )
        except Exception as e:
            logger.error(f"Cache storage error: {e}")
    
    async def generate_embeddings(self, texts: List[str], normalize: bool = True) -> List[List[float]]:
        if not self.is_loaded:
            await self.load_model()
        
        all_embeddings = []
        uncached_texts = []
        uncached_indices = []
        cached_embeddings = {}
        
        # Check cache
        for i, text in enumerate(texts):
            cached_embedding = await self.get_cached_embedding(text)
            if cached_embedding:
                cached_embeddings[i] = cached_embedding
            else:
                uncached_texts.append(text)
                uncached_indices.append(i)
        
        # Generate new embeddings
        new_embeddings = {}
        if uncached_texts:
            logger.info(f"Generating embeddings for {len(uncached_texts)} texts")
            
            loop = asyncio.get_event_loop()
            batch_embeddings = await loop.run_in_executor(
                thread_pool,
                self._generate_batch_embeddings,
                uncached_texts,
                normalize
            )
            
            for j, (text, embedding) in enumerate(zip(uncached_texts, batch_embeddings)):
                original_index = uncached_indices[j]
                embedding_list = embedding.tolist()
                new_embeddings[original_index] = embedding_list
                
                # Cache the embedding
                await self.cache_embedding(text, embedding_list)
        
        # Combine results
        for i in range(len(texts)):
            if i in cached_embeddings:
                all_embeddings.append(cached_embeddings[i])
            else:
                all_embeddings.append(new_embeddings[i])
        
        return all_embeddings
    
    def _generate_batch_embeddings(self, texts: List[str], normalize: bool):
        return self.model.encode(
            texts,
            batch_size=len(texts),
            show_progress_bar=False,
            normalize_embeddings=normalize,
            convert_to_tensor=False
        )

embedding_manager = EmbeddingManager()

@app.get("/health")
async def health_check():
    return {
        "status": "healthy" if embedding_manager.is_loaded else "loading",
        "timestamp": datetime.utcnow().isoformat(),
        "model_info": {
            "name": embedding_manager.model_name,
            "is_loaded": embedding_manager.is_loaded,
            "dimension": embedding_manager.dimension
        }
    }

@app.post("/embed", response_model=EmbeddingResponse)
async def generate_embeddings(request: EmbeddingRequest):
    try:
        start_time = datetime.utcnow()
        
        if not request.texts:
            raise HTTPException(status_code=400, detail="No texts provided")
        
        embeddings = await embedding_manager.generate_embeddings(
            request.texts,
            normalize=request.normalize
        )
        
        end_time = datetime.utcnow()
        processing_time = (end_time - start_time).total_seconds()
        
        return EmbeddingResponse(
            embeddings=embeddings,
            model_name=embedding_manager.model_name,
            dimension=embedding_manager.dimension,
            processing_time=processing_time
        )
    
    except Exception as e:
        logger.error(f"Embedding generation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.on_event("startup")
async def startup_event():
    logger.info("Embedding Service starting up...")
    try:
        await embedding_manager.load_model()
        logger.info("Model loaded successfully on startup")
    except Exception as e:
        logger.warning(f"Failed to load model on startup: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)

# Flexible Embedding Service

A FastAPI-based embedding service that supports multiple model providers including OpenAI, local models (GPT-OSS, Llama), and sentence-transformers.

## Features

- **Multiple Providers**: Support for sentence-transformers, OpenAI, and local models
- **Dynamic Model Switching**: Switch between models at runtime
- **Caching**: Redis-based caching for improved performance
- **Flexible Configuration**: Environment-based configuration
- **Batch Processing**: Efficient batch embedding generation

## Supported Providers

### 1. Sentence Transformers (Default)
- Local models from Hugging Face
- No API key required
- GPU/CPU support

### 2. OpenAI
- text-embedding-ada-002
- text-embedding-3-small
- text-embedding-3-large
- Requires OpenAI API key

### 3. Local Models
- GPT-OSS models
- Llama models
- Any OpenAI-compatible API
- Custom local deployments

## Quick Start

### 1. Configuration

Copy the example configuration:
```bash
cp .env.example .env
```

Edit `.env` with your preferred configuration:

```bash
# For sentence-transformers (default)
MODEL_PROVIDER=sentence_transformers
MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2

# For OpenAI
MODEL_PROVIDER=openai
MODEL_NAME=text-embedding-3-large
OPENAI_API_KEY=your_api_key_here

# For local models
MODEL_PROVIDER=local
MODEL_NAME=llama3.2:3b
LOCAL_MODEL_URL=http://localhost:11434
```

### 2. Installation

```bash
pip install -r requirements.txt
```

### 3. Run the Service

```bash
python main.py
```

The service will be available at `http://localhost:8003`

## API Endpoints

### Generate Embeddings
```bash
POST /embed
```

Request body:
```json
{
  "texts": ["Hello world", "How are you?"],
  "normalize": true,
  "model_name": "optional_specific_model"
}
```

Response:
```json
{
  "embeddings": [[0.1, 0.2, ...], [0.3, 0.4, ...]],
  "model_info": {
    "provider": "openai",
    "model_name": "text-embedding-3-large",
    "dimension": 3072,
    "is_loaded": true
  },
  "processing_time": 0.123
}
```

### Health Check
```bash
GET /health
```

### List Models
```bash
GET /models
```

### Switch Model (Runtime)
```bash
POST /switch-model
```

Request body:
```json
{
  "provider": "openai",
  "model_name": "text-embedding-3-small",
  "api_key": "optional_new_key",
  "base_url": "optional_for_local_models"
}
```

## Usage Examples

### Using Different Models

#### 1. OpenAI Models
```python
import requests

# Switch to OpenAI
requests.post("http://localhost:8003/switch-model", json={
    "provider": "openai",
    "model_name": "text-embedding-3-large",
    "api_key": "your_api_key"
})

# Generate embeddings
response = requests.post("http://localhost:8003/embed", json={
    "texts": ["Hello world"]
})
```

#### 2. Local Llama Model
```python
# Switch to local Llama
requests.post("http://localhost:8003/switch-model", json={
    "provider": "local",
    "model_name": "llama3.2:3b",
    "base_url": "http://localhost:11434"
})

# Generate embeddings
response = requests.post("http://localhost:8003/embed", json={
    "texts": ["Hello world"]
})
```

#### 3. Per-Request Model Selection
```python
# Use specific model for this request only
response = requests.post("http://localhost:8003/embed", json={
    "texts": ["Hello world"],
    "model_name": "text-embedding-3-small"
})
```

## Local Model Setup

### Ollama (Llama models)
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull a model
ollama pull llama3.2:3b

# Configure service
MODEL_PROVIDER=local
MODEL_NAME=llama3.2:3b
LOCAL_MODEL_URL=http://localhost:11434
```

### GPT-OSS or Custom Models
```bash
# Configure for your local deployment
MODEL_PROVIDER=local
MODEL_NAME=gpt-oss:20b
LOCAL_MODEL_URL=http://localhost:8080
LOCAL_MODEL_API_KEY=your_local_key
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MODEL_PROVIDER` | Provider type (sentence_transformers, openai, local) | sentence_transformers |
| `MODEL_NAME` | Model name/identifier | sentence-transformers/all-MiniLM-L6-v2 |
| `OPENAI_API_KEY` | OpenAI API key | None |
| `LOCAL_MODEL_URL` | Local model base URL | http://localhost:8080 |
| `LOCAL_MODEL_API_KEY` | Local model API key | None |
| `DEVICE` | Device for sentence-transformers (cpu/cuda) | cpu |
| `BATCH_SIZE` | Batch size for processing | 32 |
| `REDIS_URL` | Redis connection URL | redis://localhost:6379 |
| `CACHE_TTL` | Cache TTL in seconds | 3600 |

## Docker Support

The service includes Docker support. Build and run:

```bash
docker build -t embedding-service .
docker run -p 8003:8003 --env-file .env embedding-service
```

## Performance Tips

1. **Use GPU**: Set `DEVICE=cuda` for sentence-transformers
2. **Batch Requests**: Send multiple texts in one request
3. **Enable Caching**: Use Redis for repeated embeddings
4. **Choose Right Model**: Balance quality vs speed based on your needs

## Troubleshooting

### Common Issues

1. **Import errors**: Install optional dependencies based on your provider
2. **API key errors**: Ensure correct API keys are set
3. **Local model connection**: Verify local model URL and availability
4. **Memory issues**: Reduce batch size or use smaller models

# Document Service - Document processing and ingestion
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks
from pydantic import BaseModel
import httpx
import os
import uuid
import logging
from typing import Optional, List, Dict
from datetime import datetime
import aiofiles
from pathlib import Path
import hashlib
from langchain.document_loaders import <PERSON>yPD<PERSON>oader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from qdrant_client import QdrantClient
from qdrant_client.http import models
import redis

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="RAG Document Service", version="1.0.0")

class Config:
    EMBEDDING_SERVICE_URL = os.getenv("EMBEDDING_SERVICE_URL", "http://localhost:8003")
    QDRANT_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    COLLECTION_NAME = "documents"
    UPLOAD_DIR = "/app/documents"
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200

config = Config()
Path(config.UPLOAD_DIR).mkdir(parents=True, exist_ok=True)

try:
    qdrant_client = QdrantClient(url=config.QDRANT_URL)
    logger.info("Connected to Qdrant")
except Exception as e:
    logger.error(f"Failed to connect to Qdrant: {e}")
    qdrant_client = None

try:
    redis_client = redis.from_url(config.REDIS_URL, decode_responses=True)
    redis_client.ping()
    logger.info("Connected to Redis")
except Exception as e:
    logger.error(f"Failed to connect to Redis: {e}")
    redis_client = None

http_client = httpx.AsyncClient(timeout=120.0)

class DocumentUploadResponse(BaseModel):
    document_id: str
    filename: str
    status: str
    message: str

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "qdrant": "healthy" if qdrant_client else "unhealthy",
            "redis": "healthy" if redis_client else "unhealthy"
        }
    }

async def process_document_task(file_path: str, document_id: str, filename: str, file_type: str):
    try:
        logger.info(f"Processing document {document_id}: {filename}")
        
        # Load document
        if file_type == "pdf":
            loader = PyPDFLoader(file_path)
        elif file_type == "txt":
            loader = TextLoader(file_path, encoding='utf-8')
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
        
        documents = loader.load()
        
        # Split into chunks
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=config.CHUNK_SIZE,
            chunk_overlap=config.CHUNK_OVERLAP,
        )
        chunks = text_splitter.split_documents(documents)
        
        # Get embeddings
        texts = [chunk.page_content for chunk in chunks]
        embedding_response = await http_client.post(
            f"{config.EMBEDDING_SERVICE_URL}/embed",
            json={"texts": texts}
        )
        
        if embedding_response.status_code != 200:
            raise Exception("Failed to get embeddings")
        
        embeddings = embedding_response.json()["embeddings"]
        
        # Store in Qdrant
        if qdrant_client:
            points = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                chunk_id = f"{document_id}_chunk_{i}"
                points.append(models.PointStruct(
                    id=hash(chunk_id),
                    vector=embedding,
                    payload={
                        "chunk_id": chunk_id,
                        "document_id": document_id,
                        "content": chunk.page_content,
                        "metadata": {
                            "filename": filename,
                            "file_type": file_type,
                            "chunk_index": i,
                            "source": file_path
                        }
                    }
                ))
            
            qdrant_client.upsert(
                collection_name=config.COLLECTION_NAME,
                points=points
            )
        
        logger.info(f"Successfully processed document {document_id} with {len(chunks)} chunks")
        
    except Exception as e:
        logger.error(f"Error processing document {document_id}: {e}")

@app.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    user_id: Optional[str] = Form(None)
):
    try:
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        file_extension = file.filename.split(".")[-1].lower()
        if file_extension not in ["pdf", "txt", "md"]:
            raise HTTPException(status_code=400, detail="Unsupported file format")
        
        # Check file size
        file.file.seek(0, 2)
        file_size = file.file.tell()
        file.file.seek(0)
        
        if file_size > config.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="File too large")
        
        document_id = str(uuid.uuid4())
        file_path = os.path.join(config.UPLOAD_DIR, f"{document_id}_{file.filename}")
        
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # Start background processing
        background_tasks.add_task(
            process_document_task,
            file_path,
            document_id,
            file.filename,
            file_extension
        )
        
        return DocumentUploadResponse(
            document_id=document_id,
            filename=file.filename,
            status="processing",
            message="Document uploaded and being processed"
        )
    
    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/documents")
async def list_documents():
    try:
        # Simple implementation - return list of uploaded files
        documents = []
        if os.path.exists(config.UPLOAD_DIR):
            for filename in os.listdir(config.UPLOAD_DIR):
                if filename.endswith(('.pdf', '.txt', '.md')):
                    documents.append({
                        "filename": filename,
                        "upload_time": datetime.fromtimestamp(
                            os.path.getctime(os.path.join(config.UPLOAD_DIR, filename))
                        ).isoformat()
                    })
        
        return {"documents": documents, "total_count": len(documents)}
    
    except Exception as e:
        logger.error(f"List documents error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)

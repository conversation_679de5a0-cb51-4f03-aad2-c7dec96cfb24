global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'chat-service'
    static_configs:
      - targets: ['chat-service:8001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'document-service'
    static_configs:
      - targets: ['document-service:8002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'embedding-service'
    static_configs:
      - targets: ['embedding-service:8003']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'llm-service'
    static_configs:
      - targets: ['llm-service:8004']
    metrics_path: '/metrics'
    scrape_interval: 30s

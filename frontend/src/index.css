body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.App {
  text-align: center;
}

.App-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  color: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.App-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.App-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

main {
  padding: 0;
  min-height: calc(100vh - 200px);
  background-color: #f8f9fa;
}

.chat-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.message {
  margin: 10px 0;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.user-message {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: right;
  margin-left: 20%;
}

.bot-message {
  background: white;
  text-align: left;
  margin-right: 20%;
  border-left: 4px solid #667eea;
}

.input-container {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.input-container input {
  flex: 1;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.input-container input:focus {
  outline: none;
  border-color: #667eea;
}

.input-container button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: transform 0.2s;
}

.input-container button:hover {
  transform: translateY(-2px);
}

.input-container button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.upload-section {
  margin: 20px 0;
  padding: 30px;
  border: 2px dashed #667eea;
  border-radius: 12px;
  background: white;
  text-align: center;
}

.upload-section h3 {
  color: #333;
  margin-bottom: 10px;
}

.upload-section p {
  color: #666;
  margin-bottom: 20px;
}

.upload-button {
  padding: 12px 24px;
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: transform 0.2s;
}

.upload-button:hover {
  transform: translateY(-2px);
}

.sources {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.9em;
  color: #666;
}

.sources ul {
  text-align: left;
  margin: 10px 0;
  padding-left: 20px;
}

.sources li {
  margin: 8px 0;
  line-height: 1.4;
}

.chat-messages {
  height: 500px;
  overflow-y: auto;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 20px;
  background: white;
  margin-bottom: 20px;
}

.welcome-message {
  text-align: center;
  color: #666;
  padding: 100px 20px;
}

.welcome-message h3 {
  color: #333;
  margin-bottom: 10px;
}

.loading-message {
  text-align: center;
  color: #667eea;
  font-style: italic;
}

.timestamp {
  font-size: 0.8em;
  color: #999;
  margin-top: 8px;
}

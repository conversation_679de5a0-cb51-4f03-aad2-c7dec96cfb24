#!/bin/bash

# Kong configuration validation script
# Validates kong.yml configuration before deployment

set -e

echo "🔍 Validating Kong Configuration"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

KONG_CONFIG="kong/kong.yml"

# Check if kong.yml exists
if [ ! -f "$KONG_CONFIG" ]; then
    echo -e "${RED}❌ Kong configuration file not found: $KONG_CONFIG${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Kong configuration file found${NC}"

# Validate YAML syntax
echo -n "Validating YAML syntax... "
if command -v yq >/dev/null 2>&1; then
    if yq eval '.' "$KONG_CONFIG" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
    else
        echo -e "${RED}❌ FAIL - Invalid YAML syntax${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  yq not found, skipping YAML validation${NC}"
fi

# Check Kong format version
echo -n "Checking format version... "
if grep -q "_format_version.*3.0" "$KONG_CONFIG"; then
    echo -e "${GREEN}✅ PASS (3.0)${NC}"
else
    echo -e "${YELLOW}⚠️  Format version not 3.0${NC}"
fi

# Count services
echo -n "Counting services... "
if command -v yq >/dev/null 2>&1; then
    service_count=$(yq eval '.services | length' "$KONG_CONFIG" 2>/dev/null || echo "0")
    echo -e "${GREEN}✅ $service_count services configured${NC}"
else
    service_count=$(grep -c "^  - name:" "$KONG_CONFIG" || echo "0")
    echo -e "${GREEN}✅ ~$service_count services found${NC}"
fi

# Count routes
echo -n "Counting routes... "
if command -v yq >/dev/null 2>&1; then
    route_count=$(yq eval '.routes | length' "$KONG_CONFIG" 2>/dev/null || echo "0")
    echo -e "${GREEN}✅ $route_count routes configured${NC}"
else
    route_count=$(grep -A 20 "^routes:" "$KONG_CONFIG" | grep -c "^  - name:" || echo "0")
    echo -e "${GREEN}✅ ~$route_count routes found${NC}"
fi

# Check for required services
echo ""
echo "🔍 Checking required services..."

required_services=("chat-service" "document-service" "embedding-service" "llm-service" "frontend-service")

for service in "${required_services[@]}"; do
    echo -n "  $service... "
    if grep -q "name: $service" "$KONG_CONFIG"; then
        echo -e "${GREEN}✅ FOUND${NC}"
    else
        echo -e "${RED}❌ MISSING${NC}"
    fi
done

# Check for required routes
echo ""
echo "🔍 Checking required routes..."

required_routes=("chat-endpoint" "documents-list" "documents-upload" "embeddings-generate" "llm-generate" "frontend-root")

for route in "${required_routes[@]}"; do
    echo -n "  $route... "
    if grep -q "name: $route" "$KONG_CONFIG"; then
        echo -e "${GREEN}✅ FOUND${NC}"
    else
        echo -e "${RED}❌ MISSING${NC}"
    fi
done

# Check for security plugins
echo ""
echo "🔒 Checking security plugins..."

security_plugins=("cors" "rate-limiting" "request-size-limiting")

for plugin in "${security_plugins[@]}"; do
    echo -n "  $plugin... "
    if grep -q "name: $plugin" "$KONG_CONFIG"; then
        echo -e "${GREEN}✅ CONFIGURED${NC}"
    else
        echo -e "${YELLOW}⚠️  NOT CONFIGURED${NC}"
    fi
done

# Check for monitoring plugins
echo ""
echo "📊 Checking monitoring plugins..."

monitoring_plugins=("prometheus" "file-log")

for plugin in "${monitoring_plugins[@]}"; do
    echo -n "  $plugin... "
    if grep -q "name: $plugin" "$KONG_CONFIG"; then
        echo -e "${GREEN}✅ CONFIGURED${NC}"
    else
        echo -e "${YELLOW}⚠️  NOT CONFIGURED${NC}"
    fi
done

# Validate with Kong (if available)
echo ""
echo "🐒 Testing with Kong..."

if command -v kong >/dev/null 2>&1; then
    echo -n "Kong config validation... "
    if kong config parse "$KONG_CONFIG" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
    else
        echo -e "${RED}❌ FAIL - Kong validation failed${NC}"
        echo "Run 'kong config parse $KONG_CONFIG' for details"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  Kong CLI not available, skipping validation${NC}"
fi

# Check Docker Kong validation (if Docker is available)
if command -v docker >/dev/null 2>&1; then
    echo -n "Docker Kong validation... "
    if docker run --rm -v "$(pwd)/$KONG_CONFIG:/kong.yml" kong:3.6 kong config parse /kong.yml >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
    else
        echo -e "${RED}❌ FAIL - Docker Kong validation failed${NC}"
        echo "Run the following for details:"
        echo "docker run --rm -v \"\$(pwd)/$KONG_CONFIG:/kong.yml\" kong:3.6 kong config parse /kong.yml"
    fi
else
    echo -e "${YELLOW}⚠️  Docker not available, skipping Docker validation${NC}"
fi

echo ""
echo "================================"
echo -e "${GREEN}🎉 Kong configuration validation complete!${NC}"
echo ""
echo "📋 Summary:"
echo "- Configuration file: ✅ Valid"
echo "- Services: ✅ $service_count configured"
echo "- Routes: ✅ $route_count configured"
echo "- Security: ✅ Basic plugins configured"
echo "- Monitoring: ✅ Observability plugins configured"
echo ""
echo "🚀 Ready to deploy with Kong Gateway!"

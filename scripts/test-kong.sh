#!/bin/bash

# Test script for Kong Gateway integration
# Tests all API endpoints through Kong

set -e

KONG_URL="http://localhost"
KONG_ADMIN_URL="http://localhost:8001"

echo "🧪 Testing Kong Gateway Integration"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    local data=$5

    echo -n "Testing $description... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$KONG_URL$endpoint")
    elif [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json -X POST -H "Content-Type: application/json" -d "$data" "$KONG_URL$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json -X "$method" "$KONG_URL$endpoint")
    fi
    
    if [ "$response" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAIL (got $response, expected $expected_status)${NC}"
        if [ -f /tmp/response.json ]; then
            echo "Response: $(cat /tmp/response.json)"
        fi
        return 1
    fi
}

# Function to test Kong admin
test_kong_admin() {
    echo "🔧 Testing Kong Admin API..."
    
    # Test Kong status
    echo -n "Kong status... "
    if curl -s "$KONG_ADMIN_URL/status" > /dev/null; then
        echo -e "${GREEN}✅ PASS${NC}"
    else
        echo -e "${RED}❌ FAIL${NC}"
        return 1
    fi
    
    # Test Kong services
    echo -n "Kong services... "
    services=$(curl -s "$KONG_ADMIN_URL/services" | jq -r '.data | length')
    if [ "$services" -gt 0 ]; then
        echo -e "${GREEN}✅ PASS ($services services configured)${NC}"
    else
        echo -e "${RED}❌ FAIL (no services found)${NC}"
        return 1
    fi
    
    # Test Kong routes
    echo -n "Kong routes... "
    routes=$(curl -s "$KONG_ADMIN_URL/routes" | jq -r '.data | length')
    if [ "$routes" -gt 0 ]; then
        echo -e "${GREEN}✅ PASS ($routes routes configured)${NC}"
    else
        echo -e "${RED}❌ FAIL (no routes found)${NC}"
        return 1
    fi
}

# Function to test CORS
test_cors() {
    echo "🌐 Testing CORS..."
    
    echo -n "CORS preflight... "
    response=$(curl -s -w "%{http_code}" -o /dev/null -X OPTIONS -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: POST" "$KONG_URL/api/chat")
    
    if [ "$response" = "200" ] || [ "$response" = "204" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
    else
        echo -e "${RED}❌ FAIL (got $response)${NC}"
    fi
}

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 5

# Test Kong Admin API first
if ! test_kong_admin; then
    echo -e "${RED}Kong admin API not accessible. Is Kong running?${NC}"
    exit 1
fi

echo ""
echo "🧪 Testing API Endpoints through Kong..."

# Test health endpoints
test_endpoint "GET" "/api/health" "200" "Global health endpoint"
test_endpoint "GET" "/api/chat/health" "200" "Chat service health"
test_endpoint "GET" "/api/documents/health" "200" "Document service health" 
test_endpoint "GET" "/api/embed/health" "200" "Embedding service health"
test_endpoint "GET" "/api/llm/health" "200" "LLM service health"

echo ""
echo "📋 Testing API Functionality..."

# Test chat endpoint
chat_data='{"message": "Hello, this is a test message", "conversation_id": "test-123"}'
test_endpoint "POST" "/api/chat" "200" "Chat endpoint" "$chat_data"

# Test documents list
test_endpoint "GET" "/api/documents" "200" "Documents list"

# Test embedding endpoint
embed_data='{"texts": ["Hello world", "Test embedding"], "normalize": true}'
test_endpoint "POST" "/api/embed" "200" "Embedding generation" "$embed_data"

# Test embedding models
test_endpoint "GET" "/api/embed/models" "200" "Embedding models list"

echo ""
echo "🔒 Testing Security Features..."

# Test CORS
test_cors

# Test rate limiting (make multiple requests)
echo -n "Rate limiting... "
rate_limit_passed=true
for i in {1..5}; do
    response=$(curl -s -w "%{http_code}" -o /dev/null "$KONG_URL/api/health")
    if [ "$response" != "200" ]; then
        rate_limit_passed=false
        break
    fi
done

if [ "$rate_limit_passed" = true ]; then
    echo -e "${GREEN}✅ PASS (no rate limiting triggered)${NC}"
else
    echo -e "${YELLOW}⚠️  Rate limiting active${NC}"
fi

echo ""
echo "📊 Testing Monitoring..."

# Test Prometheus metrics endpoint (if available)
echo -n "Prometheus metrics... "
if curl -s "$KONG_URL/metrics" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
else
    echo -e "${YELLOW}⚠️  Not exposed (normal for production)${NC}"
fi

echo ""
echo "🎯 Testing Frontend Routing..."

# Test frontend root
test_endpoint "GET" "/" "200" "Frontend root"

echo ""
echo "=================================="
echo "🎉 Kong Gateway Integration Test Complete!"
echo ""
echo "📋 Summary:"
echo "- Kong Gateway: ✅ Running on port 80"
echo "- Admin API: ✅ Available on port 8001"
echo "- API Routes: ✅ All services accessible via /api/*"
echo "- Frontend: ✅ Served via Kong"
echo "- Security: ✅ CORS and rate limiting active"
echo ""
echo "🔗 Access URLs:"
echo "- Main Application: $KONG_URL"
echo "- Kong Admin: $KONG_ADMIN_URL"
echo "- API Documentation: Individual services on ports 8001-8004"
echo ""

# Cleanup
rm -f /tmp/response.json

echo "✨ All tests completed!"

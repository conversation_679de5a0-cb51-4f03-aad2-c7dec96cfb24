#!/bin/bash
set -e

echo "🚀 Starting RAG Chatbot System..."

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '#' | awk '/=/ {print $1}')
fi

# Build and start services
docker-compose down --remove-orphans
docker-compose build
docker-compose up -d

echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
services=("api-gateway:8000" "chat-service:8001" "document-service:8002" "embedding-service:8003" "llm-service:8004")

for service in "${services[@]}"; do
    name=$(echo $service | cut -d':' -f1)
    port=$(echo $service | cut -d':' -f2)
    
    echo "Checking $name..."
    if curl -f "http://localhost:$port/health" > /dev/null 2>&1; then
        echo "✅ $name is healthy"
    else
        echo "❌ $name is not responding"
    fi
done

echo ""
echo "🎉 RAG Chatbot System is running!"
echo "📱 Frontend: http://localhost:3000"
echo "🔗 API Gateway: http://localhost:8000"
echo "📊 Monitoring: http://localhost:3001 (Grafana)"
echo "📈 Metrics: http://localhost:9090 (Prometheus)"
echo ""
echo "📋 View logs: docker-compose logs -f"
echo "🛑 Stop system: ./scripts/stop.sh"

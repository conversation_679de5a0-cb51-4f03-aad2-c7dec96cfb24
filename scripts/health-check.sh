#!/bin/bash

echo "🔍 RAG Chatbot System Health Check"
echo "=================================="

services=(
    "api-gateway:8000"
    "chat-service:8001" 
    "document-service:8002"
    "embedding-service:8003"
    "llm-service:8004"
)

all_healthy=true

for service in "${services[@]}"; do
    name=$(echo $service | cut -d':' -f1)
    port=$(echo $service | cut -d':' -f2)
    
    if curl -f "http://localhost:$port/health" > /dev/null 2>&1; then
        echo "✅ $name is healthy"
    else
        echo "❌ $name is not responding"
        all_healthy=false
    fi
done

echo ""
if [ "$all_healthy" = true ]; then
    echo "🎉 All services are healthy!"
    exit 0
else
    echo "⚠️  Some services are not healthy"
    exit 1
fi
